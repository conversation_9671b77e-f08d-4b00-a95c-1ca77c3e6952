<flc-app-header [showTemplate]="true" [headerTopTitle]="headerTopTitleTpl" [headerBtn]="headerBtnTmpl">
  <ng-template #headerTopTitleTpl>
    <div class="head-title">
      <span *ngIf="pageMode === PageEditStatusEnum.add">{{ 'receiptManagement.新建收款单' | translate }}</span>
      <span *ngIf="pageMode === PageEditStatusEnum.edit">{{ 'receiptManagement.编辑收款单' | translate }}</span>
      <span *ngIf="pageMode === PageEditStatusEnum.read">{{ 'receiptManagement.收款单详情' | translate }}</span>
      <span class="header-tip" [class]="'status-' + detail?.status" *ngIf="detail?.status">{{ detail.status_label }}</span>
    </div>
  </ng-template>

  <ng-template #headerBtnTmpl>
    <div class="btn-box">
      <!-- 返回按钮 -->
      <button nz-button flButton="default" [nzShape]="'round'" (click)="onBack()">
        {{ 'flss.btn.back' | translate }}
      </button>

      <button
        *ngIf="
          ['read'].includes(pageMode) &&
          [OrderStatus.toModify, OrderStatus.toPayee].includes(orderStatus) &&
          userActions.includes('settlement:receipt-management-approval')
        "
        nz-button
        flButton="default"
        [nzShape]="'round'"
        (click)="cancel()">
        {{ 'flss.btn.cancel' | translate }}
      </button>
      <!-- 查看模式按钮 -->
      <ng-container
        *ngIf="
          ['read'].includes(pageMode) &&
          ![OrderStatus.toAudit, OrderStatus.toModifyAudit, OrderStatus.cancelled, OrderStatus.toPayee, OrderStatus.payed].includes(
            orderStatus
          ) &&
          userActions.includes('settlement:receipt-management-update')
        ">
        <button nz-button flButton="pretty-primary" [nzShape]="'round'" (click)="onSwitchToEdit()">
          <i nz-icon nzType="edit"></i>
          {{ 'flss.btn.edit' | translate }}
        </button>
      </ng-container>
      <!-- 新建/编辑模式按钮 -->
      <ng-container *ngIf="pageMode !== PageEditStatusEnum.read">
        <button
          *ngIf="
            ['add', 'edit'].includes(pageMode) &&
            [OrderStatus.toSubmit].includes(orderStatus) &&
            (userActions.includes('settlement:receipt-management-create') || userActions.includes('settlement:receipt-management-update'))
          "
          nz-button
          flButton="pretty-minor"
          [nzShape]="'round'"
          [nzLoading]="saving"
          (click)="onSubmit(1)">
          {{ 'flss.btn.save_temp' | translate }}
        </button>
        <button
          *ngIf="
            ['add', 'edit'].includes(pageMode) &&
            (userActions.includes('settlement:receipt-management-create') || userActions.includes('settlement:receipt-management-update'))
          "
          nz-button
          flButton="pretty-primary"
          [nzShape]="'round'"
          [nzLoading]="saving"
          (click)="onSubmit(2)">
          <i nz-icon [nzIconfont]="'icon-tijiao'"></i>
          {{ 'flss.btn.commit' | translate }}
        </button>
      </ng-container>

      <ng-container
        *ngIf="
          ['read'].includes(pageMode) &&
          [OrderStatus.toAudit, OrderStatus.toModifyAudit].includes(orderStatus) &&
          userActions.includes('settlement:receipt-management-approval')
        ">
        <button nz-button flButton="fault-minor" [nzShape]="'round'" (click)="modify()" [flcDisableOnClick]="1000">
          {{ 'btn.modify' | translate }}
        </button>

        <button nz-button flButton="pretty-primary" [nzShape]="'round'" (click)="pass()">
          {{ 'btn.pass' | translate }}
        </button>
      </ng-container>

      <button
        *ngIf="['read'].includes(pageMode) && [OrderStatus.toPayee].includes(orderStatus) &&
          userActions.includes('settlement:receipt-management-approval')"
        nz-button
        flButton="pretty-primary"
        [nzShape]="'round'"
        (click)="payee()">
        {{ 'flss.btn.payee' | translate }}
      </button>
    </div>
  </ng-template>
</flc-app-header>

<nz-spin class="receipt-detail-container" [nzSpinning]="loading">
  <form nz-form [formGroup]="receiptForm" class="receipt-form">
    <!-- 退回修改 -->
    <div
      *ngIf="[OrderStatus.toModify, OrderStatus.modifyAuditReturn].includes(orderStatus) && detail?.back_reason"
      class="reason-container">
      <i nz-icon [nzIconfont]="'icon-cuowu'" class="icon-cuowu"></i>
      <span>审核未通过 </span>
      <flc-text-truncated [data]="'原因：' + detail?.back_reason"></flc-text-truncated>
    </div>
    <!-- 基本信息 -->
    <div class="form-section">
      <!-- <div class="section-title">基本信息</div> -->
      <div class="form-content" *ngIf="pageMode === PageEditStatusEnum.edit || pageMode === PageEditStatusEnum.add">
        <div nz-row [nzGutter]="24">
          <div nz-col [nzSpan]="formItem.itemSpan" *ngFor="let formItem of formConfig">
            <nz-form-item>
              <nz-form-label [nzRequired]="formItem.required" [nzSpan]="formItem.labelSpan">{{
                translateName + formItem.label | translate
              }}</nz-form-label>
              <ng-container *ngIf="formItem.key === 'code'">
                <nz-form-control [nzSpan]="formItem.controlSpan" [flcErrorTip]="translateName + formItem.label | translate">
                  <input nz-input formControlName="code" placeholder="系统自动生成" />
                </nz-form-control>
              </ng-container>
              <ng-container *ngIf="formItem.type === 'select'">
                <nz-form-control [nzSpan]="formItem.controlSpan" [flcErrorTip]="translateName + formItem.label | translate">
                  <nz-select
                    nzAllowClear
                    [nzShowSearch]="true"
                    [formControlName]="formItem.key"
                    [nzPlaceHolder]="'flss.placeholder.select' | translate"
                    [nzDisabled]="formItem.disabled"
                    (ngModelChange)="poSelectChange($event, formItem)">
                    <nz-option
                      [nzValue]="option[formItem.valueKey]"
                      [nzLabel]="option[formItem.labelKey]"
                      [nzDisabled]="option.disable"
                      *ngFor="let option of this[formItem.optionKey]">
                    </nz-option>
                  </nz-select>
                </nz-form-control>
              </ng-container>
              <ng-container *ngIf="formItem.type === 'date'"
                ><nz-form-control [nzSpan]="formItem.controlSpan" [flcErrorTip]="translateName + formItem.label | translate">
                  <nz-date-picker
                    [nzFormat]="formItem.nzMode ? '' : 'yyyy/MM/dd'"
                    [nzMode]="formItem.nzMode"
                    [formControlName]="formItem.key"></nz-date-picker> </nz-form-control
              ></ng-container>
              <ng-container *ngIf="formItem.type === 'input-number'">
                <nz-form-control [nzSpan]="formItem.controlSpan" [flcErrorTip]="translateName + formItem.label | translate">
                  <nz-input-number-group [nzAddOnAfter]="formItem.unit_name">
                    <nz-input-number
                      [formControlName]="formItem.key"
                      [nzMin]="formItem.min"
                      [nzMax]="formItem.max"
                      [nzPrecision]="formItem.precision"
                      [nzDisabled]="formItem.disabled"
                      [nzPlaceHolder]="'flss.placeholder.input' | translate"></nz-input-number>
                  </nz-input-number-group>
                </nz-form-control>
              </ng-container>
              <ng-container *ngIf="formItem.type === 'input-area'"
                ><nz-form-control [nzSpan]="formItem.controlSpan" [flcErrorTip]="translateName + formItem.label | translate">
                  <nz-textarea-count [nzMaxCharacterCount]="formItem.maxlength" class="inline-count">
                    <textarea
                      [nzAutosize]="{ minRows: 1, maxRows: formItem.maxRows }"
                      [formControlName]="formItem.key"
                      nz-input
                      [maxlength]="formItem.maxlength"></textarea>
                  </nz-textarea-count> </nz-form-control
              ></ng-container>
            </nz-form-item>
          </div>
        </div>
      </div>

      <div class="form-content" *ngIf="pageMode === PageEditStatusEnum.read">
        <div nz-row [nzGutter]="24">
          <div nz-col [nzSpan]="formItem.itemSpan" *ngFor="let formItem of formConfig">
            <nz-form-item>
              <nz-form-label [nzRequired]="formItem.required" [nzSpan]="formItem.labelSpan">{{
                translateName + formItem.label | translate
              }}</nz-form-label>
              <nz-form-control [nzSpan]="formItem.controlSpan">
                <!-- 收款类型显示 -->
                <ng-container *ngIf="formItem.key === 'type'">
                  <flc-text-truncated [data]="detail?.type == 1 ? '预收款' : '应收款'"></flc-text-truncated>
                </ng-container>
                <!-- 客户显示 -->
                <ng-container *ngIf="formItem.key === 'customer_id'">
                  <flc-text-truncated [data]="detail?.customer_name"></flc-text-truncated>
                </ng-container>
                <!-- 收款账户显示 -->
                <ng-container *ngIf="formItem.key === 'account_id'">
                  <flc-text-truncated [data]="detail?.bank_name + ':' + detail?.bank_account"></flc-text-truncated>
                </ng-container>
                <!-- 币种显示 -->
                <ng-container *ngIf="formItem.key === 'currency_id'">
                  <flc-text-truncated [data]="detail?.currency_name"></flc-text-truncated>
                </ng-container>
                <!-- 日期显示 -->
                <ng-container *ngIf="formItem.type === 'date'">
                  <flc-text-truncated
                    [data]="detail?.[formItem.key] ? (detail?.[formItem.key] | date: 'yyyy/MM/dd') : null"></flc-text-truncated>
                </ng-container>
                <!-- 其他字段显示 -->
                <ng-container
                  *ngIf="formItem.type !== 'date' && !['type', 'customer_id', 'account_id', 'currency_id'].includes(formItem.key)">
                  <flc-text-truncated [data]="detail?.[formItem.key]"></flc-text-truncated>
                </ng-container>
              </nz-form-control>
            </nz-form-item>
          </div>
        </div>
      </div>
    </div>
  </form>
  <!-- 预收款明细 -->
  <div class="form-section" *ngIf="receiptForm.get('type')?.value === 1">
    <div class="section-title">
      <span>预收款明细</span>
      <button *ngIf="pageMode !== PageEditStatusEnum.read" nz-button flButton="minor" [nzShape]="'round'" (click)="onAddAdvanceDetail()">
        <i nz-icon nzType="plus"></i>
        添加明细
      </button>
    </div>

    <div class="table-container">
      <!-- <flc-table #advanceTable [tableHeader]="advanceTableHeader" [tableConfig]="advanceTableConfig" [template]="advanceTableTemplate">
      </flc-table> -->
      <nz-table
        #advanceTable
        [nzData]="advanceTableConfig.dataList"
        [nzScroll]="{ x: '100%' }"
        [nzShowPagination]="false"
        [nzFrontPagination]="false"
        [nzLoading]="advanceTableConfig.loading"
        [nzPageSize]="advanceTableConfig.pageSize"
        [nzPageIndex]="advanceTableConfig.pageIndex"
        [nzTotal]="advanceTableConfig.count">
        <thead>
          <tr>
            <th nzLeft nzWidth="36px">
              <a class="setting-btn" [ngStyle]="{ color: btnHighLight ? '#4D96FF' : '' }" (click)="changeHeader($event, 1)">
                <i nz-icon nzType="setting" nzTheme="fill"></i>
              </a>
            </th>

            <ng-container *ngFor="let item of advanceTableHeader">
              <th
                [nzLeft]="item.disable || item.pinned"
                nz-resizable
                *ngIf="item.visible && !(item.key === 'action' && pageMode === PageEditStatusEnum.read)"
                nzPreview
                [nzWidth]="item.width"
                (nzResizeEnd)="onResize($event, item.label, 1)">
                {{ translateName + item.label | translate
                }}<i
                  [nzPopoverOverlayClassName]="'tips-bg'"
                  *ngIf="item.tips"
                  class="hint-icon"
                  nz-popover
                  nzPopoverTitle=""
                  [nzPopoverContent]="item.tips"
                  nzPopoverTrigger="hover"
                  [nzPopoverBackdrop]="true"
                  nz-icon
                  [nzIconfont]="'icon-tishi'"></i>
                <div class="resize-trigger"></div>
                <nz-resize-handle nzDirection="right">
                  <div class="resize-trigger"></div>
                </nz-resize-handle>
              </th>
            </ng-container>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let item of advanceTableConfig.dataList; index as i">
            <td nzLeft="0px">{{ i + 1 }}</td>
            <ng-container *ngFor="let col of advanceTableHeader">
              <td
                [nzLeft]="col.disable || col.pinned"
                *ngIf="col.visible && !(col.key === 'action' && pageMode === PageEditStatusEnum.read)">
                <ng-container *ngIf="col.type === 'text'">
                  <flc-text-truncated [data]="item[col.key]"></flc-text-truncated>
                </ng-container>
                <ng-container *ngIf="col.type === 'input-number'">
                  <ng-container *ngIf="col.key === 'ratio'">
                    <ng-container *ngIf="pageMode !== PageEditStatusEnum.read">
                      <nz-input-number-group [nzAddOnAfter]="'%'">
                        <nz-input-number
                          [(ngModel)]="item[col.key]"
                          [nzMin]="0"
                          [nzMax]="100"
                          [nzStep]="0.01"
                          [nzPrecision]="2"
                          nzSize="small"
                          (ngModelChange)="onAdvanceRatioChange(item, $event)"
                          style="width: 120px">
                        </nz-input-number>
                      </nz-input-number-group>
                    </ng-container>
                    <ng-container *ngIf="pageMode === PageEditStatusEnum.read">
                      <flc-text-truncated [data]="item[col.key]"></flc-text-truncated>
                    </ng-container>
                  </ng-container>
                  <ng-container *ngIf="col.key !== 'ratio'">
                    <ng-container *ngIf="pageMode !== PageEditStatusEnum.read">
                      <nz-input-number
                        [(ngModel)]="item[col.key]"
                        [nzMin]="0"
                        [nzMax]="col.key === 'payment' ? item.estimated_amount : 999999999"
                        [nzStep]="0.01"
                        [nzPrecision]="2"
                        nzSize="small"
                        (ngModelChange)="
                          col.key == 'payment' ? onAdvanceAmountChange(item, $event) : onAdvanceAmountLocalChange(item, $event)
                        "
                        style="width: 120px">
                      </nz-input-number>
                    </ng-container>
                    <ng-container *ngIf="pageMode === PageEditStatusEnum.read">
                      <flc-text-truncated [data]="item[col.key]"></flc-text-truncated>
                    </ng-container>
                  </ng-container>
                </ng-container>
                <ng-container *ngIf="col.type === 'action' && pageMode !== PageEditStatusEnum.read">
                  <button
                    nz-button
                    nzType="link"
                    flButton="link"
                    nz-tooltip
                    nzTooltipTitle="删除"
                    (click)="onDeleteAdvanceDetail(index, 1)">
                    <i nz-icon nzIconfont="icon-caozuolan_shanchu1" class="delete-hover"></i>
                  </button>
                </ng-container>
              </td>
            </ng-container>
          </tr>
        </tbody>
        <tfoot>
          <tr style="text-align: center" class="summary-row">
            <td nzLeft="0px"></td>
            <!-- <td >合计</td> -->
            <ng-container *ngFor="let col of advanceTableHeader">
              <td
                [nzLeft]="col.disable || col.pinned"
                *ngIf="col.visible && !(col.key === 'action' && pageMode === PageEditStatusEnum.read)">
                <ng-container *ngIf="col.key === 'order_code'">
                  <strong>合计</strong>
                </ng-container>
                <ng-container *ngIf="col.key === 'ratio'">
                  {{ advanceSummary.totalRadio }}
                </ng-container>
                <ng-container *ngIf="col.key === 'order_qty'">
                  {{ advanceSummary.totalQty }}
                </ng-container>
                <ng-container *ngIf="col.key === 'estimated_amount'">
                  {{ advanceSummary.totalEstimatedAmount }}
                </ng-container>
                <ng-container *ngIf="col.key === 'pre_receiving_amount'">
                  {{ advanceSummary.totalPreReceivingAmount }}
                </ng-container>
                <ng-container *ngIf="col.key === 'pre_receiving_amount_local'">
                  {{ advanceSummary.totalPreReceivingAmountLocal }}
                </ng-container>
                <ng-container *ngIf="col.key === 'received_amount'">
                  {{ advanceSummary.totalReceivedAmount }}
                </ng-container>
                <ng-container *ngIf="col.key === 'received_amount_local'">
                  {{ advanceSummary.totalReceivedAmountLocal }}
                </ng-container>
                <ng-container *ngIf="col.key === 'payment'">
                  {{ advanceSummary.totalPayment }}
                </ng-container>
                <ng-container *ngIf="col.key === 'payment_local'">
                  {{ advanceSummary.totalPaymentLocal }}
                </ng-container>
              </td>
            </ng-container>
          </tr>
        </tfoot>
      </nz-table>
    </div>
  </div>

  <!-- 应收款明细 -->
  <div class="form-section" *ngIf="receiptForm.get('type')?.value === 2">
    <div class="section-title">
      <span>应收款明细</span>
      <button *ngIf="pageMode !== PageEditStatusEnum.read" nz-button flButton="minor" [nzShape]="'round'" (click)="onAddReceivableDetail()">
        <i nz-icon nzType="plus"></i>
        添加明细
      </button>
    </div>

    <div class="table-container">
      <nz-table
        #receivableTable
        [nzData]="receivableTableConfig.dataList"
        [nzScroll]="{ x: '100%' }"
        [nzShowPagination]="false"
        [nzFrontPagination]="false"
        [nzLoading]="receivableTableConfig.loading"
        [nzPageSize]="receivableTableConfig.pageSize"
        [nzPageIndex]="receivableTableConfig.pageIndex"
        [nzTotal]="receivableTableConfig.count">
        <thead>
          <tr>
            <th nzLeft nzWidth="36px">
              <a class="setting-btn" [ngStyle]="{ color: btnHighLight ? '#4D96FF' : '' }" (click)="changeHeader($event, 2)">
                <i nz-icon nzType="setting" nzTheme="fill"></i>
              </a>
            </th>

            <ng-container *ngFor="let item of receivableTableHeader">
              <th
                [nzLeft]="item.disable || item.pinned"
                nz-resizable
                *ngIf="item.visible && !(item.key === 'action' && pageMode === PageEditStatusEnum.read)"
                nzPreview
                [nzWidth]="item.width"
                (nzResizeEnd)="onResize($event, item.label, 2)">
                <ng-container *ngIf="item.key === 'used_advance_amount' && pageMode !== PageEditStatusEnum.read; else normalHeader"
                  >{{ translateName + item.label | translate }}
                  <label nz-checkbox [(ngModel)]="autoUseAdvance" (ngModelChange)="onAutoUseAdvanceChange($event)">自动占用</label>
                </ng-container>
                <ng-template #normalHeader>
                  {{ translateName + item.label | translate }}
                </ng-template>
                <i
                  [nzPopoverOverlayClassName]="'tips-bg'"
                  *ngIf="item.tips"
                  class="hint-icon"
                  nz-popover
                  nzPopoverTitle=""
                  [nzPopoverContent]="item.tips"
                  nzPopoverTrigger="hover"
                  [nzPopoverBackdrop]="true"
                  nz-icon
                  [nzIconfont]="'icon-tishi'"></i>
                <div class="resize-trigger"></div>
                <nz-resize-handle nzDirection="right">
                  <div class="resize-trigger"></div>
                </nz-resize-handle>
              </th>
            </ng-container>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let item of receivableTableConfig.dataList; index as i">
            <td nzLeft="0px">{{ i + 1 }}</td>
            <ng-container *ngFor="let col of receivableTableHeader">
              <td
                [nzLeft]="col.disable || col.pinned"
                [class.merged-cell]="(col.key === 'bills_receivable_code' && !shouldShowReceivableCode(i)) ||
                                   (col.key === 'outbound_code' && !shouldShowOutboundCode(i)) ||
                                   (col.key === 'order_code' && !shouldShowOrderCode(i))"
                [class.merge-border]="(col.key === 'bills_receivable_code' && shouldShowReceivableCode(i) && i > 0) ||
                                    (col.key === 'outbound_code' && shouldShowOutboundCode(i) && i > 0 && !shouldShowReceivableCode(i)) ||
                                    (col.key === 'order_code' && shouldShowOrderCode(i) && i > 0 && !shouldShowOutboundCode(i))"
                *ngIf="col.visible && !(col.key === 'action' && pageMode === PageEditStatusEnum.read)">

                <!-- 应收单号 - 相同应收单号时合并显示 -->
                <ng-container *ngIf="col.key === 'bills_receivable_code'">
                  <ng-container *ngIf="shouldShowReceivableCode(i)">
                    <flc-text-truncated [data]="item.bills_receivable_code"></flc-text-truncated>
                  </ng-container>
                </ng-container>

                <!-- 出库单号 - 相同出库单号时合并显示 -->
                <ng-container *ngIf="col.key === 'outbound_code'">
                  <ng-container *ngIf="shouldShowOutboundCode(i)">
                    <flc-text-truncated [data]="item.outbound_code"></flc-text-truncated>
                  </ng-container>
                </ng-container>

                <!-- 订单号 - 相同订单号时合并显示 -->
                <ng-container *ngIf="col.key === 'order_code'">
                  <ng-container *ngIf="shouldShowOrderCode(i)">
                    <flc-text-truncated [data]="item.order_code"></flc-text-truncated>
                  </ng-container>
                </ng-container>

                <!-- 交付单号 - 每行独立显示 -->
                <ng-container *ngIf="col.key === 'po_code'">
                  <flc-text-truncated [data]="item.po_code || '-'"></flc-text-truncated>
                </ng-container>

                <!-- 款号 -->
                <ng-container *ngIf="col.key === 'style_code'">
                  <flc-text-truncated [data]="item.style_code || '-'"></flc-text-truncated>
                </ng-container>

                <!-- 品名 -->
                <ng-container *ngIf="col.key === 'category'">
                  <flc-text-truncated [data]="item.category || '-'"></flc-text-truncated>
                </ng-container>

                <!-- 数量 -->
                <ng-container *ngIf="col.key === 'quantity'">
                  <flc-text-truncated [data]="item.quantity | number: '1.0-0'"></flc-text-truncated>
                </ng-container>

                <!-- 应收金额 -->
                <ng-container *ngIf="col.key === 'receivable_amount'">
                  <flc-text-truncated [data]="item.receivable_amount"></flc-text-truncated>
                </ng-container>

                <!-- 已收金额 -->
                <ng-container *ngIf="col.key === 'received_amount'">
                  <flc-text-truncated [data]="item.received_amount"></flc-text-truncated>
                </ng-container>

                <!-- 待收金额 -->
                <ng-container *ngIf="col.key === 'pending_amount'">
                  <flc-text-truncated [data]="item.pending_amount"></flc-text-truncated>
                </ng-container>

                <!-- 预收款(可占用) - 特殊格式：已占用(剩余可占用) -->
                <ng-container *ngIf="col.key === 'advance_amount'">
                  <div class="advance-amount-display">
                    <span class="used-amount">{{ preReceiptAmount[item.order_uuid] || '0' }}</span> &nbsp;&nbsp;
                    <span class="remaining-amount">({{ canUsedAmount[item.order_uuid] || '0' }})</span>
                  </div>
                </ng-container>

                <!-- 占用预收款 -->
                <ng-container *ngIf="col.key === 'used_advance_amount'">
                  <ng-container *ngIf="pageMode !== PageEditStatusEnum.read">
                    <div class="advance-input-group">
                      <nz-input-number
                        [style.border-color]="item.mark ? 'red' : ''"
                        [(ngModel)]="item.used_advance_amount"
                        [nzMin]="0"
                        [nzStep]="0.01"
                        [nzPrecision]="2"
                        nzSize="small"
                        (ngModelChange)="onReceivableUsedAdvanceChange(item, $event)"
                        style="width: 100px">
                      </nz-input-number>
                      <div style="color: red" *ngIf="item.mark">不得超过可占用</div>
                    </div>
                  </ng-container>
                  <ng-container *ngIf="pageMode === PageEditStatusEnum.read">
                    <flc-text-truncated [data]="item.used_advance_amount"></flc-text-truncated>
                  </ng-container>
                </ng-container>

                <!-- 本次收款 -->
                <ng-container *ngIf="col.key === 'current_receipt_amount'">
                  <ng-container *ngIf="pageMode !== PageEditStatusEnum.read">
                    <nz-input-number
                      [style.border-color]="item.currentMark ? 'red' : ''"
                      [(ngModel)]="item.current_receipt_amount"
                      [nzMin]="0.01"
                      [nzStep]="0.01"
                      [nzPrecision]="2"
                      nzSize="small"
                      (nzBlur)="onBlurCurrentAmount(item, $event)"
                      (ngModelChange)="onReceivableCurrentAmountChange(item, $event)"
                      style="width: 120px">
                    </nz-input-number>
                  </ng-container>
                  <ng-container *ngIf="pageMode === PageEditStatusEnum.read">
                    <flc-text-truncated [data]="item.current_receipt_amount"></flc-text-truncated>
                  </ng-container>
                </ng-container>

                <!-- 本次收款（本币） -->
                <ng-container *ngIf="col.key === 'current_receipt_amount_local'">
                  <ng-container *ngIf="pageMode !== PageEditStatusEnum.read">
                    <nz-input-number
                      [(ngModel)]="item.current_receipt_amount_local"
                      [nzMin]="0"
                      [nzStep]="0.01"
                      [nzPrecision]="2"
                      nzSize="small"
                      (ngModelChange)="onReceivableCurrentAmountLocalChange(item, $event)"
                      style="width: 120px">
                    </nz-input-number>
                  </ng-container>
                  <ng-container *ngIf="pageMode === PageEditStatusEnum.read">
                    <flc-text-truncated [data]="item.current_receipt_amount_local"></flc-text-truncated>
                  </ng-container>
                </ng-container>

                <!-- 操作列 -->
                <ng-container *ngIf="col.key === 'action' && pageMode !== PageEditStatusEnum.read">
                  <button nz-button nzType="link" flButton="link" nz-tooltip nzTooltipTitle="删除" (click)="onDeleteReceivableDetail(i)">
                    <i nz-icon nzIconfont="icon-caozuolan_shanchu1" class="delete-hover"></i>
                  </button>
                </ng-container>
              </td>
            </ng-container>
          </tr>
        </tbody>
        <tfoot>
          <tr style="text-align: center" class="summary-row">
            <td nzLeft="0px"></td>
            <ng-container *ngFor="let col of receivableTableHeader">
              <td
                [nzLeft]="col.disable || col.pinned"
                *ngIf="col.visible && !(col.key === 'action' && pageMode === PageEditStatusEnum.read)">
                <ng-container *ngIf="col.key === 'bills_receivable_code'">
                  <strong>合计</strong>
                </ng-container>
                <ng-container *ngIf="col.key === 'quantity'">
                  {{ receivableSummary.totalQuantity | number: '1.0-0' }}
                </ng-container>
                <ng-container *ngIf="col.key === 'receivable_amount'">
                  {{ receivableSummary.totalReceivableAmount }}
                </ng-container>
                <ng-container *ngIf="col.key === 'received_amount'">
                  {{ receivableSummary.totalReceivedAmount }}
                </ng-container>
                <ng-container *ngIf="col.key === 'pending_amount'">
                  {{ receivableSummary.totalPendingAmount }}
                </ng-container>
                <ng-container *ngIf="col.key === 'advance_amount'">
                  {{ receivableSummary.totalAdvanceAmount }}
                </ng-container>
                <ng-container *ngIf="col.key === 'used_advance_amount'">
                  {{ receivableSummary.totalUsedAdvanceAmount }}
                </ng-container>
                <ng-container *ngIf="col.key === 'current_receipt_amount'">
                  {{ receivableSummary.totalCurrentReceiptAmount }}
                </ng-container>
                <ng-container *ngIf="col.key === 'current_receipt_amount_local'">
                  {{ receivableSummary.totalCurrentReceiptAmountLocal }}
                </ng-container>
              </td>
            </ng-container>
          </tr>
        </tfoot>
      </nz-table>
    </div>
  </div>
</nz-spin>
