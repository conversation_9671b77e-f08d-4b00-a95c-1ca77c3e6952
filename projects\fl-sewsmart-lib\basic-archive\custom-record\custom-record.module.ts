import { CommonModule } from '@angular/common';
import { HttpClient } from '@angular/common/http';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { TranslateLoader, TranslateModule, TranslateService } from '@ngx-translate/core';
import { FlcComponentsModule, FlcDirectivesModule, FlcDrawerHelperService, FlcPipesModule, FlcTableHelperModule } from 'fl-common-lib';
import { FlButtonModule } from 'fl-ui-angular';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzDatePickerModule } from 'ng-zorro-antd/date-picker';
import { NzDrawerModule } from 'ng-zorro-antd/drawer';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzInputNumberModule } from 'ng-zorro-antd/input-number';
import { NzMessageModule } from 'ng-zorro-antd/message';
import { NzModalModule } from 'ng-zorro-antd/modal';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzSwitchModule } from 'ng-zorro-antd/switch';
import { NzTableModule } from 'ng-zorro-antd/table';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';
import { MultiTranslateHttpLoader } from 'ngx-translate-multi-http-loader';
import { CustomRecordRoutingModule } from './custom-record-routing.module';
import { CustomRecordService } from './custom-record.service';
import { customRecordListComponent } from './list/custom-record-list.component';
import { CustomRecordDetailComponent } from './detail/custom-record-detail.component';
import { BasicArchiveComponentsModule } from '../components/basic-archive-components.module';
import { NzCascaderModule } from 'ng-zorro-antd/cascader';
import { NzSpinModule } from 'ng-zorro-antd/spin';
import { CurrencyDetailModalComponent } from './components/currenncy-detail-modal/currenncy-detail-modal.component';

const ngModules = [CommonModule, ReactiveFormsModule, FormsModule];
const flCommonModules = [FlcComponentsModule, FlcPipesModule, FlcDirectivesModule, FlcTableHelperModule];
const nzModules = [
  NzIconModule,
  NzButtonModule,
  NzTableModule,
  NzModalModule,
  NzDatePickerModule,
  NzToolTipModule,
  NzCascaderModule,
  NzSelectModule,
  NzInputModule,
  NzMessageModule,
  NzDrawerModule,
  NzFormModule,
  NzInputNumberModule,
  NzSwitchModule,
  NzSpinModule,
];

@NgModule({
  imports: [
    ...ngModules,
    ...nzModules,
    ...flCommonModules,
    CustomRecordRoutingModule,
    BasicArchiveComponentsModule,
    FlButtonModule,
    TranslateModule.forChild({
      isolate: false,
      extend: true,
      loader: {
        provide: TranslateLoader,
        useFactory: (http: HttpClient) => {
          return new MultiTranslateHttpLoader(http, [
            { prefix: './assets/i18n/basic-archive/custom-record/', suffix: '.json' },
            { prefix: './assets/i18n/basic-archive/components/', suffix: '.json' },
          ]);
        },
        deps: [HttpClient],
      },
    }),
  ],
  declarations: [customRecordListComponent, CustomRecordDetailComponent, CurrencyDetailModalComponent],
  providers: [FlcDrawerHelperService, CustomRecordService],
})
export class CustomRecordModule {
  constructor(public translateService: TranslateService, private _service: CustomRecordService) {
    const cl = translateService.currentLang;
    translateService.currentLang = '';
    translateService.use(cl).subscribe(() => {
      this._service.translateEventEmitter.next();
    });
  }
}
