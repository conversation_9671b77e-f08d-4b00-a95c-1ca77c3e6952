<div class="outer-container">
  <div #searchBar>
    <flc-search-container
      [headerTitle]="translateName + '客户档案' | translate"
      [btnTpl]="btnTpl"
      (reset)="onReset()"
      (handleFold)="onFold()">
      <ng-template #btnTpl>
        <button
          nz-button
          flButton="pretty-primary"
          [nzShape]="env_project === 'deepflow' ? null : 'round'"
          (click)="onAdd()"
          *ngIf="permissionMap?.create">
          <i nz-icon [nzIconfont]="'icon-xinjian1'"></i>
          {{ 'flss.btn.add' | translate }}
        </button>
      </ng-template>
      <div class="search-item-container" *ngFor="let items of searchOptions">
        <span class="search-name">
          {{ translateName + items.label | translate }}
        </span>
        <div class="search-option-{{ items.type }}" [ngSwitch]="items.type">
          <ng-container *ngSwitchCase="'local-select'">
            <nz-select
              [(ngModel)]="searchData[items.key]"
              style="min-width: 120px"
              nzAllowClear
              nzShowSearch
              (ngModelChange)="onChangeDataList(true)"
              [nzPlaceHolder]="
                items?.placeHolder ? (translateName + items?.placeHolder | translate) : ('flss.placeholder.select' | translate)
              ">
              <nz-option *ngFor="let item of items?.options" [nzValue]="item?.value" [nzLabel]="item?.label"></nz-option>
            </nz-select>
          </ng-container>
          <ng-container *ngSwitchCase="'dateRange'">
            <nz-range-picker
              [(ngModel)]="searchData[items.key]"
              [nzPlaceHolder]="['flss.placeholder.start' | translate, 'flss.placeholder.end' | translate]"
              (ngModelChange)="onChangeDataList(true)"
              [nzSuffixIcon]="''"></nz-range-picker>
          </ng-container>
          <ng-container *ngSwitchCase="'input'">
            <input
              nz-input
              style="width: auto"
              type="text"
              [(ngModel)]="searchData[items.key]"
              [placeholder]="items?.placeHolder ? (translateName + items?.placeHolder | translate) : ('flss.placeholder.input' | translate)"
              (ngModelChange)="onChangeDataList(true)" />
          </ng-container>
        </div>
      </div>
    </flc-search-container>
  </div>

  <div class="table-container">
    <flc-table
      #customTable
      [tableHeader]="tableHeader"
      [tableConfig]="tableConfig"
      [template]="tableTemplate"
      [batchActionTemplate]="batchActionTemplate"
      (getCount)="onSelectedCount($event)"
      (sizeChanges)="onSizeChanges($event)"
      (indexChanges)="onIndexChange($event)">
    </flc-table>

    <ng-template #tableTemplate let-data="data">
      <!-- 操作列 -->
      <ng-container *ngIf="data.isAction">
        <div class="operate-col">
          <flss-archive-table-td-action
            [uaType]="actionConfig.uaType"
            [uaKey]="actionConfig.uaKey"
            [modeType]="actionConfig.modeType"
            [popoverTextConfig]="{ type: translateName + actionConfig.popoverTextConfigType | translate }"
            [data]="data?.item"
            [extraActionConfig]="extraActionConfig"
            (status)="onToggleSingleStatus($event)"
            (detail)="onDetail(data.item.id)"
            (action)="onAction($event)"></flss-archive-table-td-action>
        </div>
      </ng-container>
    </ng-template>

    <ng-template #batchActionTemplate>
      <flss-archive-batch-action
        [uaType]="actionConfig.uaType"
        [uaKey]="actionConfig.uaKey"
        [count]="selectedCount"
        (batchAction)="onBatchAction($event)"></flss-archive-batch-action>
    </ng-template>
  </div>
</div>
