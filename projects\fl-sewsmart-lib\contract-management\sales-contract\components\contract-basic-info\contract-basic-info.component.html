<form nz-form [formGroup]="basicInfoForm" *ngIf="editMode === pageEditStatusEnum.edit || editMode === pageEditStatusEnum.add">
  <div nz-row>
    <div nz-col [nzSpan]="formItem.itemSpan" *ngFor="let formItem of formConfig">
      <nz-form-item>
        <nz-form-label [nzSpan]="formItem.labelSpan" [nzRequired]="formItem.required">{{
          translateLabel + formItem.label | translate
        }}</nz-form-label>
        <ng-container *ngIf="formItem.type === 'select'">
          <nz-form-control [nzSpan]="formItem.controlSpan" [flcErrorTip]="translateLabel + formItem.label | translate">
            <nz-select
              [nzShowSearch]="true"
              [formControlName]="formItem.key"
              [nzPlaceHolder]="'flss.placeholder.select' | translate"
              nzAllowClear>
              <nz-option
                [nzValue]="option[formItem.valueKey]"
                [nzLabel]="option[formItem.labelKey]"
                [nzDisabled]="option.disable"
                *ngFor="let option of createOptions?.[formItem.optionKey]">
              </nz-option>
            </nz-select>
          </nz-form-control>
        </ng-container>

        <ng-container *ngIf="formItem.type === 'remote-select'">
          <nz-form-control [nzSpan]="formItem.controlSpan" [flcErrorTip]="translateLabel + formItem.label | translate">
            <flc-dynamic-search-select
              [dataUrl]="formItem.url!"
              [transData]="{ value: formItem?.transformDataValue ?? 'value', label: formItem?.transformDataLabel ?? 'label' }"
              [formControlName]="formItem.key"
              [column]="formItem.column"
              [optAlwaysReload]="true"
              [canSearch]="true"
              [dataParam]="formItem?.dataParam ?? 'option_list'"
              [payLoad]="formItem?.payload ?? null"
              [defaultValue]="{ value: basicInfoForm?.get(formItem.key)?.value, label: basicInfoForm?.get(formItem.onlyReadKey)?.value }"
              (handleSearch)="onDynamicSearch($event, formItem)">
            </flc-dynamic-search-select>
          </nz-form-control>
        </ng-container>

        <ng-container *ngIf="formItem.type === 'input'">
          <nz-form-control [nzSpan]="formItem.controlSpan" [flcErrorTip]="translateLabel + formItem.label | translate">
            <nz-input-group
              [nzSuffix]="formItem?.maxlength ? basicInfoForm?.get(formItem.key)?.value?.length + 0 + '/' + formItem?.maxlength : ''">
              <input
                flcInputTrim
                [formControlName]="formItem.key"
                nz-input
                [placeholder]="'flss.placeholder.input' | translate"
                [maxlength]="formItem.maxlength"
                [ngClass]="{ 'ant-input-have-maxlength': formItem.maxlength || false }" />
            </nz-input-group>
          </nz-form-control>
        </ng-container>

        <ng-container *ngIf="formItem.type === 'textarea'">
          <nz-form-control [nzSpan]="formItem.controlSpan" [flcErrorTip]="translateLabel + formItem.label | translate">
            <nz-textarea-count [nzMaxCharacterCount]="formItem.maxlength" class="inline-count">
              <textarea
                nz-input
                [formControlName]="formItem.key"
                [placeholder]="'flss.placeholder.input' | translate"
                [maxLength]="formItem.maxlength"
                [nzAutosize]="{ minRows: 1, maxRows: 4 }"
                flcInputTrim></textarea>
            </nz-textarea-count>
          </nz-form-control>
        </ng-container>

        <ng-container *ngIf="formItem.type === 'input-number'">
          <nz-form-control [nzSpan]="formItem.controlSpan" [flcErrorTip]="translateLabel + formItem.label | translate">
            <nz-input-number-group>
              <nz-input-number
                style="width: 100%"
                [formControlName]="formItem.key"
                [nzPlaceHolder]="'flss.placeholder.input' | translate"
                [nzMin]="formItem?.min"
                [nzPrecision]="formItem?.precision"
                [nzMax]="formItem?.max">
              </nz-input-number>
            </nz-input-number-group>
          </nz-form-control>
        </ng-container>

        <ng-container *ngIf="formItem.type === 'cascader'">
          <nz-form-control [nzSpan]="formItem.controlSpan" [flcErrorTip]="translateLabel + formItem.label | translate">
            <nz-cascader
              [formControlName]="formItem.key"
              [nzOptions]="regionList"
              [nzPlaceHolder]="'flss.placeholder.select' | translate"
              (nzSelectionChange)="addressChange($event, formItem)"
              [nzShowSearch]="true"></nz-cascader>
          </nz-form-control>
        </ng-container>

        <ng-container *ngIf="formItem.type === 'text'">
          <nz-form-control [nzSpan]="formItem.controlSpan">
            <!-- 人民币金额特殊处理：当值为null时显示"-" -->
            <flc-text-truncated *ngIf="formItem.key !== 'rmb_contract_amount'" [data]="basicInfoForm?.get(formItem.key)?.value">
            </flc-text-truncated>
            <flc-text-truncated
              *ngIf="formItem.key === 'rmb_contract_amount'"
              [data]="
                basicInfoForm?.get(formItem.key)?.value === null || basicInfoForm?.get(formItem.key)?.value == 0
                  ? '-'
                  : basicInfoForm?.get(formItem.key)?.value
              ">
            </flc-text-truncated>
          </nz-form-control>
        </ng-container>

        <ng-container *ngIf="formItem.type === 'datePicker'">
          <nz-form-control [nzSpan]="formItem.controlSpan" [flcErrorTip]="translateLabel + formItem.label | translate">
            <nz-date-picker style="width: 100%" [nzFormat]="'yyyy/MM/dd'" [formControlName]="formItem.key"></nz-date-picker>
          </nz-form-control>
        </ng-container>
      </nz-form-item>
    </div>
  </div>
</form>

<div nz-row *ngIf="editMode === pageEditStatusEnum.read">
  <div nz-col [nzSpan]="formItem.itemSpan" *ngFor="let formItem of formConfig">
    <nz-form-item>
      <nz-form-label [nzSpan]="formItem.labelSpan" [nzRequired]="formItem.required">{{
        translateLabel + formItem.label | translate
      }}</nz-form-label>
      <nz-form-control [nzSpan]="formItem.controlSpan">
        <!-- 人民币金额特殊处理：当值为null时显示"-" -->
        <flc-text-truncated
          *ngIf="formItem.type !== 'datePicker' && formItem.key !== 'rmb_contract_amount'"
          [data]="detailInfo?.[formItem.onlyReadKey]">
        </flc-text-truncated>
        <flc-text-truncated
          *ngIf="formItem.type !== 'datePicker' && formItem.key === 'rmb_contract_amount'"
          [data]="(detailInfo?.[formItem.onlyReadKey] === null || detailInfo?.[formItem.onlyReadKey] === 0) ? '-' : detailInfo?.[formItem.onlyReadKey]">
        </flc-text-truncated>
        <flc-text-truncated
          *ngIf="formItem.type === 'datePicker'"
          [data]="detailInfo?.[formItem.onlyReadKey] ? (detailInfo?.[formItem.onlyReadKey] | date: 'yyyy/MM/dd') : null">
        </flc-text-truncated>
      </nz-form-control>
    </nz-form-item>
  </div>
</div>
