.custor-modal-body {
  padding: 0 8px;

  h4 {
    margin: 8px 0;
  }
}
.custor-modalfooter {
  padding: 8px 16px;
  text-align: right;
  background: transparent;
  border-top: 1px solid #f0f0f0;

  button:last-child {
    margin-left: 8px;
  }
}
:host ::ng-deep {
  .ant-form-item {
    align-items: baseline;
    margin: 0;
  }
}

.currency-modal-body {
  .customer-info {
    h4 {
      color: #333;
      font-weight: 500;
    }
    p {
      font-size: 14px;
    }
  }

  .currency-table-container {
    ::ng-deep {
      .ant-table-tbody > tr > td {
        padding: 12px 16px;
        .ant-table-expanded-row-fixed {
          width: 100% !important;
        }
        .ant-table-expanded-row-fixed::after {
          border-right: none !important;
        }
      }

      .ant-table-thead > tr > th {
        background-color: #fafafa;
        font-weight: 500;
      }
    }

    .empty-data-container {
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 200px;
      padding: 40px 20px;

      .empty-text {
        color: #999;
        font-size: 14px;
        text-align: center;
      }
    }
  }
}
