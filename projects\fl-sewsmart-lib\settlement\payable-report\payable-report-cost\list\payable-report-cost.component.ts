import { Component, OnInit, OnDestroy, ViewChild, ElementRef, ChangeDetectorRef } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { FlcModalService, FlcSearchCombinationComponent } from 'fl-common-lib';
import { PayableReportService } from '../../payable-report.service';
import { NzMessageService } from 'ng-zorro-antd/message';
import { TranslateService } from '@ngx-translate/core';
import { NzNotificationService } from 'ng-zorro-antd/notification';
import { NzModalService } from 'ng-zorro-antd/modal';
import { ReturnModelComponent } from '../../../components/return-model/return-model.component';
import {
  ExpenseSearchList,
  AllocationSearchList,
  initialSearchData,
  initialExpenseSearchData,
  initialAllocationSearchData,
  ExpenseTableHeaders,
  AllocationTableHeaders,
  SearchData,
} from './payable-report-cost.config';
import { format, startOfDay, endOfDay } from 'date-fns';
import { finalize } from 'rxjs/operators';

@Component({
  selector: 'flss-payable-report-cost',
  templateUrl: './payable-report-cost.component.html',
  styleUrls: ['./payable-report-cost.component.scss'],
  providers: [PayableReportService],
})
export class PayableReportCostComponent implements OnInit, OnDestroy {
  @ViewChild('searchContainer', { static: false }) searchContainer!: ElementRef;
  @ViewChild(FlcSearchCombinationComponent) searchComponent!: FlcSearchCombinationComponent;
  orderBy: Array<string> = []; // 排序
  // 筛选相关
  searchList: any[] = ExpenseSearchList; // 动态筛选列表
  searchData: SearchData = initialSearchData();
  selectedList: any[] = [];

  // 选中的数据
  checkedInfo!: { count: number; list: any[] };
  // 初始化标志，避免在初始化过程中触发变更检测错误
  private _initialized = false;

  // 表格相关
  tableHeaders: any[] = ExpenseTableHeaders; // 动态表格头
  tableConfig = {
    tableName: 'settlement.ExpenseList', // 一个页面多表格，且需保存表头信息时，需要设置表格名称
    dataList: <any[]>[], //表格数据
    hasCheckbox: true, //是否显示选中功能
    count: 100, //数据总数量
    pageIndex: 1, //当前页码
    pageSize: 20, //当前每页数量
    loading: false,
    settingBtnPos: 'start',
    height: 400,
    detailBtn: true,
    version: '1.0.0', // 表头版本号
    hasAction: true, // 是否有操作列
    actionWidth: '120px', // 操作列宽度
    clearSelect: false, // 是否清空选中状态
  };
  tableHeight = 500;
  optionMaps = {};

  actionMap = {
    hasCreate: false,
    hasAudit: false,
    hasType: false,
  };
  // 费用来源选项卡
  currentTab: 'expense' | 'allocation' = 'expense';

  // 总金额
  totalAmount = 0;
  allocationAmount = 0;

  // 计算属性，避免表达式变更检测错误
  get isNoSelection(): boolean {
    if (!this._initialized) {
      return true;
    }
    return this.selectedList.length === 0;
  }

  // 其他
  loading = false;
  private subscription?: Subscription;

  constructor(
    private router: Router,
    private _service: PayableReportService,
    private _router: Router,
    private messageService: NzMessageService,
    private translateService: TranslateService,
    private cdr: ChangeDetectorRef,
    private _activeRoute: ActivatedRoute,
    private _notice: NzNotificationService,
    private _flcModalService: FlcModalService,
    private _modalService: NzModalService
  ) {}

  ngOnInit(): void {
    // 使用Promise.resolve()确保在下一个微任务中执行，避免ExpressionChangedAfterItHasBeenCheckedError
    Promise.resolve().then(() => {
      this._initialized = true;
      // 确保首次进入页面时排序参数为空
      this.orderBy = [];
      this.initPage();
      // getDataList方法内部会自动设置加载状态，无需重复设置
      this.getDataList();
      this.getOptions();
    });

    const userActions = this._service.getUserActions() || [];
    this.actionMap = {
      hasCreate: userActions.includes('settlement:expense-management-create'),
      hasAudit: userActions.includes('settlement:expense-management-audit'),
      hasType: userActions.includes('settlement:expense-management-expense-type'),
    };
  }

  ngOnDestroy(): void {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
    // 确保组件销毁时关闭加载状态
    this.setTableLoading(false);
  }

  /**
   * 初始化页面
   */
  initPage(): void {
    this.resizePage();
  }

  /**
   * 设置表格加载状态
   */
  private setTableLoading(loading: boolean): void {
    this.loading = loading;
    this.tableConfig.loading = loading;
    // 创建新的tableConfig对象以触发Angular的变更检测
    this.tableConfig = { ...this.tableConfig };
    // 手动触发变更检测以确保UI及时更新
    this.cdr.detectChanges();
  }

  /**
   * 重置表格数据
   */
  private resetTableData(): void {
    this.tableConfig.dataList = [];
    this.tableConfig.count = 0;
    this.totalAmount = 0;
    this.allocationAmount = 0;
  }

  /**
   * 更改排序方式
   */
  // 更改了排序方式
  sortOrderChange({ value, key }: { value: 'desc' | 'asc' | null; key: string }) {
    this.orderBy = value ? [key + '_' + value] : [];
    console.log('排序方式改变', this.orderBy);
    this.getDataList();
  }
  /**
   * 安全地将字符串转换为数字
   */
  private parseToNumber(value: string | number | null | undefined): number {
    if (value === null || value === undefined || value === '') {
      return 0;
    }
    if (typeof value === 'number') {
      return value;
    }
    const parsed = parseInt(value.toString(), 10);
    return isNaN(parsed) ? 0 : parsed;
  }

  onInputSearchValue(e: any) {
    this.searchData.keyword = e;
    this.tableConfig.pageIndex = 1;
    this.getDataList();
  }

  /**
   * 获取数据列表
   */
  getDataList(reset = false): void {
    if (reset) {
      this.tableConfig.pageIndex = 1;
    }

    // 设置表格加载状态
    this.setTableLoading(true);

    // 根据当前选项卡调用不同的API
    const apiCall =
      this.currentTab === 'expense'
        ? this._service.getExpenseReportList(this.buildExpensePayload())
        : this._service.getAllocationReportList(this.buildAllocationPayload());

    apiCall
      .pipe(
        finalize(() => {
          // 无论成功、失败还是超时，都要关闭加载状态
          this.setTableLoading(false);
        })
      )
      .subscribe({
        next: (res: any) => {
          if (res && res.data) {
            // 根据当前选项卡处理不同的数据结构
            if (this.currentTab === 'expense') {
              // 费用支出：数据在 res.data.list 或 res.data.data 中
              this.tableConfig.dataList = res.data.list || res.data.data || [];
              this.tableConfig.count = res.data.count || 0;
              this.totalAmount = res.data.total_amount || 0;
              this.allocationAmount = res.data.total_share_amount || 0;
            } else {
              // 费用分摊：数据直接在 res.data 数组中
              if (Array.isArray(res.data)) {
                this.tableConfig.dataList = res.data;
                this.tableConfig.count = res.data.length;
              } else {
                // 如果数据在 res.data.data 中
                this.tableConfig.dataList = res.data.data || [];
                this.tableConfig.count = res.data.total ? parseInt(res.data.total) : (res.data.data || []).length;
              }

              // 计算费用分摊总金额
              this.allocationAmount = this.tableConfig.dataList.reduce((sum: number, item: any) => {
                const amount = parseFloat(item.amortization_total_amount || '0');
                return sum + (isNaN(amount) ? 0 : amount);
              }, 0);
              this.totalAmount = 0; // 费用分摊页面不显示总金额
            }
          } else {
            // 如果没有数据，重置列表和金额
            this.resetTableData();
          }

          // 数据加载完成后重置clearSelect状态，确保后续选中操作正常
          this.tableConfig.clearSelect = false;
          this.tableConfig = { ...this.tableConfig };
        },
        error: (err: any) => {
          console.error('获取数据失败:', err);
          this.messageService.error('获取数据失败');
          // 错误时重置数据
          this.resetTableData();
        },
      });
  }

  /**
   * 构建费用支出API参数
   * 严格按照接口定义的参数类型进行传参
   */
  buildExpensePayload(): any {
    const payload: any = {
      page: this.tableConfig.pageIndex,
      size: this.tableConfig.pageSize,
      order_by: this.orderBy,
      code: this.searchData.code,
      department_id: this.parseToNumber(this.searchData.department_id),
      payment_method_id: this.parseToNumber(this.searchData.payment_method_id),
      keyword: this.searchData.keyword || '',
      occurred_start_at: null,
      occurred_end_at: null,
    };

    // 发生日期范围 - 转换为时间戳(number类型)
    // 注意：配置中的valueKey是occurred_date，兼容两种字段名
    const occurredDate = this.searchData.occurred_date || this.searchData.occur_date;
    if (occurredDate && Array.isArray(occurredDate) && occurredDate.length === 2) {
      const dateRange = occurredDate as Date[];
      // 将日期转换为时间戳（毫秒）
      payload.occurred_start_at = startOfDay(dateRange[0]).getTime();
      payload.occurred_end_at = endOfDay(dateRange[1]).getTime();
    }

    console.log('费用支出API参数:', payload);
    return payload;
  }

  /**
   * 构建费用分摊API参数
   * 严格按照接口定义的参数类型进行传参
   */
  buildAllocationPayload(): any {
    // 处理费用分摊相关的筛选条件，严格按照接口参数类型
    const payload = {
      page: this.tableConfig.pageIndex,
      page_size: this.tableConfig.pageSize,
      // keyword保持string类型
      keyword: this.searchData.keyword || '',
      // 以下字段需要转换为number类型
      status: this.parseToNumber(this.searchData.status),
      gen_user_id: this.parseToNumber(this.searchData.gen_user_id),
      audit_user_id: this.parseToNumber(this.searchData.audit_user_id),
      // 时间字段初始化为0，后续根据日期范围设置
      order_by: this.orderBy,
      start_time: 0,
      end_time: 0,
      audit_start_time: 0,
      audit_end_time: 0,
    };

    // 处理创建日期范围 - 转换为时间戳(number类型)
    if (this.searchData.creation_date && Array.isArray(this.searchData.creation_date) && this.searchData.creation_date.length === 2) {
      const dateRange = this.searchData.creation_date as Date[];
      payload.start_time = startOfDay(dateRange[0]).getTime();
      payload.end_time = endOfDay(dateRange[1]).getTime();
    }

    // 处理审核日期范围 - 转换为时间戳(number类型)
    if (this.searchData.audit_date && Array.isArray(this.searchData.audit_date) && this.searchData.audit_date.length === 2) {
      const dateRange = this.searchData.audit_date as Date[];
      payload.audit_start_time = startOfDay(dateRange[0]).getTime();
      payload.audit_end_time = endOfDay(dateRange[1]).getTime();
    }

    // 调试日志：输出最终的API参数
    // console.log('费用分摊API参数:', payload);

    return payload;
  }

  /**
   * 处理筛选条件（保留原方法用于兼容）
   */
  handleWhere(): any {
    const where: any = {};

    // 根据当前选项卡确定需要处理的字段
    const relevantFields =
      this.currentTab === 'expense'
        ? ['document_no', 'department_id', 'occur_date', 'payment_method_id']
        : ['plan_id', 'status', 'gen_user_id', 'audit_user_id', 'creation_date', 'audit_date'];

    Object.entries(this.searchData).forEach(([key, value]) => {
      // 只处理当前选项卡相关的字段
      if (relevantFields.includes(key) && value !== null && value !== undefined && value !== '') {
        if (
          (key === 'occur_date' || key === 'creation_date' || key === 'audit_date') &&
          Array.isArray(value) &&
          (value as any[]).length === 2
        ) {
          // 处理日期范围
          const dateRange = value as Date[];
          if (key === 'occur_date') {
            where.occur_date_start = format(startOfDay(dateRange[0]), 'yyyy-MM-dd');
            where.occur_date_end = format(endOfDay(dateRange[1]), 'yyyy-MM-dd');
          } else if (key === 'creation_date') {
            where.start_time = format(startOfDay(dateRange[0]), 'yyyy-MM-dd HH:mm:ss');
            where.end_time = format(endOfDay(dateRange[1]), 'yyyy-MM-dd HH:mm:ss');
          } else if (key === 'audit_date') {
            where.audit_start_time = format(startOfDay(dateRange[0]), 'yyyy-MM-dd HH:mm:ss');
            where.audit_end_time = format(endOfDay(dateRange[1]), 'yyyy-MM-dd HH:mm:ss');
          }
        } else {
          where[key] = value;
        }
      }
    });

    // 添加选项卡类型到查询条件
    where.tab_type = this.currentTab;

    return where;
  }

  // 搜索条件切换
  modelChanges() {
    this.tableConfig.pageIndex = 1;
    this.getDataList();
  }

  /**
   * 搜索
   */
  onSearch(): void {
    this.getDataList(true);
  }

  /**
   * 重置
   */
  reset(): void {
    // 重置排序参数
    this.orderBy = [];

    // 根据当前选项卡重置相应的搜索数据
    if (this.currentTab === 'expense') {
      this.searchData = {
        ...initialExpenseSearchData(),
        ...initialAllocationSearchData(),
      };
    } else {
      this.searchData = {
        ...initialExpenseSearchData(),
        ...initialAllocationSearchData(),
      };
    }
    this.selectedList = [];
    this.getDataList(true);
  }

  /**
   * 刷新
   */
  refresh(): void {
    this.getDataList();
  }

  /**
   * 调整页面大小
   */
  resizePage(): void {
    setTimeout(() => {
      const searchHeight = this.searchContainer?.nativeElement?.clientHeight || 120;
      this.tableHeight = window.innerHeight - searchHeight - 200;
      if (this.tableHeight < 300) {
        this.tableHeight = 300;
      }
      this.tableConfig.height = this.tableHeight;
      this.tableConfig = { ...this.tableConfig };
      // 手动触发变更检测
      this.cdr.detectChanges();
    }, 100);
  }

  /**
   * 表格选择变化 (getCount事件)
   */
  selectedIds = [];
  selectChange(event: any): void {
    // 使用Promise.resolve()确保在下一个微任务中更新，避免变更检测错误
    Promise.resolve().then(() => {
      this.selectedList = event || [];
      this.cdr.detectChanges();
    });

    this.checkedInfo = event;
    this.selectedIds = event.list.map((item: any) => item.id);
  }

  /**
   * 分页大小变化 (sizeChanges事件)
   */
  sizeChanges(event: any): void {
    this.tableConfig.pageSize = event;
    this.getDataList(true);
  }

  /**
   * 分页索引变化 (indexChanges事件)
   */
  indexChanges(event: any): void {
    this.tableConfig.pageIndex = event;
    this.getDataList();
  }

  /**
   * 获取下拉选项数据
   */
  getOptions() {
    // 根据当前选项卡获取不同的下拉选项
    if (this.currentTab === 'expense') {
      this._service.getExpenseReportOptions().subscribe((res: any) => {
        this.optionMaps = res.data || {};
      });
    } else {
      this._service.getAllocationReportOptions().subscribe((res: any) => {
        this.optionMaps = res.data || {};
      });
    }
  }

  /**
   * 兼容旧的pageChange方法
   */
  pageChange(event: any): void {
    if (event.pageIndex !== undefined) {
      this.indexChanges(event.pageIndex);
    }
    if (event.pageSize !== undefined) {
      this.sizeChanges(event.pageSize);
    }
  }

  /**
   * 跳转详情
   * @param  {any} id
   */
  getDetails(id: any) {
    // console.log('跳转详情', id);

    // 根据当前选项卡模式决定跳转路径
    if (this.currentTab === 'allocation') {
      // 费用分摊模式：跳转到费用分摊详情页
      this._router.navigate(['../cost/allocation-detail', id], { relativeTo: this._activeRoute });
    } else {
      // 费用支出模式：跳转到费用支出详情页
      this._router.navigate(['../cost/expense-detail', id], { relativeTo: this._activeRoute });
    }
  }

  /**
   * 新增
   */
  onAdd(): void {
    // 根据当前选项卡模式决定跳转路径
    if (this.currentTab === 'allocation') {
      // 分摊模式：跳转到费用分摊详情页
      this._router.navigate(['../cost/allocation-detail', 'add'], { relativeTo: this._activeRoute });
    } else {
      // 费用支出模式：跳转到费用支出详情页
      this._router.navigate(['../cost/expense-detail', 'add'], { relativeTo: this._activeRoute });
    }
  }

  /**
   * 审核
   */
  onAudit(action: 'pass' | 'reject'): void {
    if (!this.checkedInfo.count) {
      this.messageService.warning('请至少选择一条数据');
      return;
    }

    // 验证选中数据的状态
    const selectedItems = this.checkedInfo.list;
    const invalidItems = selectedItems.filter((item: any) => item.status !== 2);

    if (invalidItems.length > 0) {
      const invalidCodes = invalidItems.map((item: any) => item.code || item.document_no).join('、');
      const actionText = action === 'pass' ? '审核' : '退回修改';
      this.messageService.warning(`费用单据编号：${invalidCodes} 不能操作${actionText}`);
      return;
    }

    // 获取选中数据的id数组
    const ids = this.selectedIds;

    if (action === 'pass') {
      this._onPass(ids);
    } else if (action === 'reject') {
      this._onReject(ids);
    }
  }

  /**
   * 审核通过
   */
  private _onPass(ids: number[]) {
    const ref = this._flcModalService.confirmCancel({ content: '确定审核通过' });
    ref.afterClose.subscribe((res) => {
      if (res) {
        // 根据当前模式调用不同的接口
        const apiCall =
          this.currentTab === 'expense' ? this._service.approveExpenseReport({ ids }) : this._service.approveAllocationReport({ ids });

        apiCall.subscribe((res: any) => {
          if (res.data) {
            this.messageService.success('审核通过');
            // 刷新列表数据
            this.getDataList();
          }
        });
      }
    });
  }

  /**
   * 审核退回
   */
  private _onReject(ids: number[]) {
    this._modalService.create({
      nzContent: ReturnModelComponent,
      nzWidth: 400,
      nzClosable: false,
      nzWrapClassName: 'flc-confirm-modal',
      nzFooter: null,
      nzOnOk: (comp: any) => {
        const _value = comp.formGroup.getRawValue();

        // 根据当前模式调用不同的接口
        const apiCall =
          this.currentTab === 'expense'
            ? this._service.rejectExpenseReport({ ids, reason: _value.reason })
            : this._service.rejectAllocationReport({ ids, reason: _value.reason });

        apiCall.subscribe((result: any) => {
          if (result.data) {
            this.messageService.success('退回成功');
            // 刷新列表数据
            this.getDataList();
          }
        });
      },
    });
  }
  /**
   * 删除
   */
  onDelete(): void {
    if (!this.checkedInfo.count) {
      this.messageService.warning('请选择要删除的数据');
      return;
    }

    // 验证选中数据的状态 - 只有待提交(status=1)和待审核(status=2)状态的单据支持删除
    const selectedItems = this.checkedInfo.list;
    const invalidItems = selectedItems.filter((item: any) => item.status !== 1 && item.status !== 2);

    if (invalidItems.length > 0) {
      const invalidCodes = invalidItems.map((item: any) => item.code || item.document_no).join('、');
      this.messageService.warning(`费用单据编号：${invalidCodes} 不支持删除操作`);
      return;
    }

    // 获取选中数据的id数组
    const ids = this.selectedIds;

    const ref = this._flcModalService.confirmCancel({ content: '确定删除选中的数据吗？' });
    ref.afterClose.subscribe((res) => {
      if (res) {
        // 根据当前模式调用不同的删除接口
        const apiCall =
          this.currentTab === 'expense' ? this._service.deleteExpenseReport({ ids }) : this._service.deleteAllocationReport({ ids });

        apiCall.subscribe((result: any) => {
          if (result.data) {
            this.messageService.success('删除成功');
            
            // 清空所有选中项相关状态
            this.checkedInfo = { count: 0, list: [] };
            this.selectedList = [];
            this.selectedIds = [];

            // 设置表格清空选中状态标志，触发表格组件清空选中
            this.tableConfig.clearSelect = true;
            this.tableConfig = { ...this.tableConfig };

            // 刷新列表数据
            this.getDataList();
          }
        });
      }
    });
  }

  /**
   * 切换费用来源选项卡
   */
  switchTab(tab: 'expense' | 'allocation'): void {
    this.currentTab = tab;

    // 清空所有选中项相关状态
    this.checkedInfo = { count: 0, list: [] };
    this.selectedList = [];
    this.selectedIds = [];

    // 设置表格清空选中状态标志，触发表格组件清空选中
    this.tableConfig.clearSelect = true;
    this.tableConfig = { ...this.tableConfig };

    // 切换表格模式的时候，需要将输入单号清空
    this.searchComponent.searchInputValue = '';
    // 重置排序参数
    this.orderBy = [];

    // 根据选项卡切换筛选条件和表格字段
    if (tab === 'expense') {
      // 费用支出：使用原有的筛选条件和表格字段
      this.searchList = ExpenseSearchList;
      this.tableHeaders = ExpenseTableHeaders;
      // 重置搜索数据为费用支出相关字段
      this.searchData = {
        ...initialExpenseSearchData(),
        ...initialAllocationSearchData(), // 保留所有字段但只显示相关的
      };
    } else {
      // 费用分摊：使用新的筛选条件和表格字段
      this.searchList = AllocationSearchList;
      this.tableHeaders = AllocationTableHeaders;
      // 重置搜索数据为费用分摊相关字段
      this.searchData = {
        ...initialExpenseSearchData(), // 保留所有字段但只显示相关的
        ...initialAllocationSearchData(),
      };
    }

    // 确保 keyword 字段置空
    this.searchData.keyword = '';

    // 手动触发变更检测，确保UI及时更新
    this.cdr.detectChanges();

    // 重新获取选项数据和列表数据
    this.getOptions();
    this.getDataList(true);
  }

  /**
   * 费用来源
   */
  onExpenseSource(): void {
    this.router.navigate(['../cost/cost-type', '1'], { relativeTo: this._activeRoute });
  }

  // 获取审核时间
  getAuditTime(value: any) {
    return (value && value > 0) ? format(value, 'yyyy/MM/dd') : null
  }
}
