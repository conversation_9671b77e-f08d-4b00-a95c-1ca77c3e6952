import { HttpClient } from '@angular/common/http';
import { EventEmitter, Injectable } from '@angular/core';
import { FlcSpKeyConstant, FlcSpUtilService } from 'fl-common-lib';
import { FormModeEnum } from './custom-record.config';
import { TranslateService } from '@ngx-translate/core';

@Injectable()
export class CustomRecordService {
  public formMode = FormModeEnum.ReadOnly;
  permissionKey = 'basic-archive:custom-record-';
  public translateEventEmitter = new EventEmitter<void>();

  public get serviceUrl(): string {
    return '/service/procurement-inventory/archive/v1';
  }

  public get searchOptionUrl(): string {
    return `${this.serviceUrl}/list_option`;
  }

  constructor(private http: HttpClient, private _spUtil: FlcSpUtilService, private _translate: TranslateService) {}

  /* 权限 */
  initUserActions() {
    let userActionMap: Map<string, Array<string>> = new Map();
    if (this._spUtil.containsObjectKey(FlcSpKeyConstant.UserActions)) {
      userActionMap = this._spUtil.getObject(FlcSpKeyConstant.UserActions) as Map<string, Array<string>>;
    }
    return userActionMap.get('basic-archive/custom-record') || [];
  }

  translateValue(key: string, params?: any): string {
    return this._translate.instant(key, params);
  }

  // 删除客户档案
  customerDelete(data: any) {
    return this.http.post<any>(`${this.serviceUrl}/customer-delete`, data);
  }

  // 客户档案选项
  customerListOptions() {
    return this.http.post<any>(`${this.serviceUrl}/customer-list-option`, {});
  }

  // 更改客户档案状态
  setCustomerStatus(data: any) {
    return this.http.post<any>(`${this.serviceUrl}/customer-status-change`, data);
  }

  // 新建修改客户档案
  setCustomer(data: any) {
    return this.http.post<any>(`${this.serviceUrl}/customer/operation`, data);
  }

  // 客户档案详情
  getCustomerDetail(data: any) {
    return this.http.post<any>(`${this.serviceUrl}/customer-detail`, data);
  }
  // 客户档案列表
  getCustomerList(data: any) {
    return this.http.post<any>(`${this.serviceUrl}/customer-list`, data);
  }
  // 客户档案筛选项
  getCustomerOptions(data: any) {
    return this.http.post<any>(`${this.serviceUrl}/customer-list-option`, data);
  }
  // 校验唯一性
  customerUnique(data: any) {
    return this.http.post<any>(`${this.serviceUrl}/customer-unique`, data);
  }

  //详情省市区
  getAddress() {
    return this.http.get<any>('/service/archive/v1/region?level=0');
  }

  // 币种
  getCurrencyList() {
    return this.http.post<any>('/service/archive/v1/unit/list', {
      page: 1,
      limit: 20,
      order_by: [],
      where: [
        {
          column: 'unit_type',
          op: '=',
          value: '9',
        },
      ],
    });
  }
}
