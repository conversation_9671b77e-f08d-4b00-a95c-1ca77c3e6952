import { Component, Input, OnInit, OnDestroy, SimpleChanges } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';

import { FlcUtilService, FlcValidatorService } from 'fl-common-lib';
import { format } from 'date-fns';

import { initBasicInfoFormConfig } from './contract-basic-info.config';
import { SalesContractService } from '../../sales-contract.service';
import { SearchOptions } from '../../models/sales-contract.interface';
import { ContractSubjectEventType, PageEditStatusEnum } from '../../models/sales-contract.enum';

@Component({
  selector: 'flss-contract-basic-info',
  templateUrl: './contract-basic-info.component.html',
  styleUrls: ['./contract-basic-info.component.scss'],
})
export class SaleContractBasicInfoComponent implements OnInit, OnDestroy {
  @Input() editMode = PageEditStatusEnum.read;
  @Input() detailInfo: any = {};
  @Input() isContentChange = false;
  @Input() id: string | number | null = null;

  createOptions: SearchOptions = {}; // 下拉数据
  regionList: any[] = []; // 省市区列表
  formConfig: any[] = initBasicInfoFormConfig();
  basicInfoForm: FormGroup = this._fb.group({});
  customerInfo: any = null; // 有客户名称带出的客户详情

  pageEditStatusEnum = PageEditStatusEnum;
  translateTitle = this._service.translateTitle;
  translateLabel = this._service.translateLabel;
  translateAction = this._service.translateAction;
  translateTip = this._service.translateTip;

  subscribeKey = 'sales-contract-basic-info';

  constructor(
    private _fb: FormBuilder,
    private flcValidatorService: FlcValidatorService,
    private _service: SalesContractService,
    private _flcUtilService: FlcUtilService
  ) {}

  ngOnInit() {
    this.subscribeEvent();
    this.getCreateOptions();
    this.getRegions();
    this.getEmployeeOptions();
    // 新建逻辑
    if (this.editMode === PageEditStatusEnum.add) {
      this.initForm();
      this.createCode();
    }
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (this?.detailInfo && this.editMode !== PageEditStatusEnum.read) {
      this.initForm(this.detailInfo);
    }
  }

  ngOnDestroy(): void {
    this._service.removeSubjectListener(this.subscribeKey);
  }

  // 订阅
  subscribeEvent() {
    this._service.addSubjectListener(this.subscribeKey, [ContractSubjectEventType.updateContractAmount], (res) => {
      switch (res.type) {
        case ContractSubjectEventType.updateContractAmount:
          this.calculateContractAmount(res.data?.data ?? []);
          break;
      }
    });
  }

  // 发送事件
  sendSubjectEvent(type: ContractSubjectEventType, data: any) {
    this._service.sendSubjectEvent(type, {
      type,
      data,
    });
  }

  // 获取下拉
  getCreateOptions() {
    this._service.getSalesContractCreateOptions().subscribe((res: any) => {
      if (res.code === 200) {
        this.createOptions = { ...this.createOptions, ...res.data };
      }
    });
  }

  // 获取业务员下拉
  getEmployeeOptions() {
    this._service.getEmployeeOptions().subscribe((res: any) => {
      if (res?.code === 200) {
        this.createOptions['employee'] = res?.data?.employees ?? [];
      }
    });
  }

  // 获取国家下拉
  getRegions() {
    this._service.getRegions().subscribe((res: any) => {
      if (res.code == 200) {
        this.regionList = this._service.onTransOption(res?.data?.children ?? []);
      }
    });
  }

  // 选择起运国和目的国
  addressChange(e: any, formItem: any) {
    formItem.relatedKey?.forEach((rk: any, index: number) => {
      this.basicInfoForm?.get(rk)?.setValue(e[index]?.value ?? null);
    });
  }

  // 生成销售合同号
  createCode() {
    this._service.createCode().subscribe((res: any) => {
      if (res.code === 200) {
        this.basicInfoForm?.get('code')?.setValue(res.data?.code ?? null, { emitViewToModelChange: false });
      }
    });
  }
  // 初始化表单
  initForm(data: any = null) {
    this.formConfig.forEach((item: any) => {
      const _control = new FormControl();
      item.required && _control.addValidators(Validators.required);
      item.maxlength && _control.addValidators(Validators.maxLength(item.maxlength));
      this.basicInfoForm.addControl(item.key, _control);
      this.basicInfoForm?.get(item.key)?.setValue(data?.[item.key] ?? null, { emitViewToModelChange: false });

      if (item?.relatedKey?.length) {
        item.relatedKey?.forEach((k: string) => {
          this.basicInfoForm?.addControl(k, new FormControl(null));
          this.basicInfoForm?.get(k)?.setValue(data?.[k] ?? null, { emitViewToModelChange: false });
        });
      }
    });
  }

  // dynamic下拉
  onDynamicSearch(e: any, formItem: any) {
    // 获取客户详情
    if (formItem?.key === 'customer_id') {
      this.getCustomerInfo(e?.value);
    }
  }

  // 调用客户档案客户详情接口，获取客户相关信息
  getCustomerInfo(val: number) {
    if (val) {
      this._service.getCustomerDetail({ id: val }).subscribe((res: any) => {
        if (res.code === 200) {
          this.customerInfo = {
            ...res.data,
            currency_name: res?.data?.unit_name,
          };
          this.setCustomerRelateField();
          this.sendSubjectEvent(ContractSubjectEventType.updateCurrencyUnit, this.customerInfo);
        }
      });
    } else {
      this.customerInfo = null;
      // 清空关联字段
      this.setCustomerRelateField();
      this.sendSubjectEvent(ContractSubjectEventType.updateCurrencyUnit, this.customerInfo);
    }
  }

  // 将客户相关字段赋值
  setCustomerRelateField() {
    // 统一社会信用代码
    this.basicInfoForm?.get('usci')?.setValue(this.customerInfo?.usci ?? null, { emitViewToModelChange: false });
    // 货币
    this.basicInfoForm?.get('currency_id')?.setValue(this.customerInfo?.unit_id ?? null, { emitViewToModelChange: false });
    this.basicInfoForm?.get('currency_name')?.setValue(this.customerInfo?.unit_name ?? null, { emitViewToModelChange: false });
    // 汇率
    this.basicInfoForm?.get('rate')?.setValue(this.customerInfo?.rate ?? null, { emitViewToModelChange: false });
    // 运输方式
    this.basicInfoForm?.get('transport_type')?.setValue(this.customerInfo?.shipping_method ?? null, { emitViewToModelChange: false });
    // 目的国

    let destination_country: number[] = [];
    if (!this.customerInfo?.receiver_country_id) {
      destination_country = [];
    } else if (!this.customerInfo?.province_id) {
      destination_country = [this.customerInfo?.receiver_country_id];
    } else if (!this.customerInfo?.city_id) {
      destination_country = [this.customerInfo?.receiver_country_id, this.customerInfo?.province_id];
    } else if (!this.customerInfo?.district_id) {
      destination_country = [this.customerInfo?.receiver_country_id, this.customerInfo?.province_id, this.customerInfo?.city_id];
    } else {
      destination_country = [
        this.customerInfo?.receiver_country_id ?? null,
        this.customerInfo?.province_id ?? null,
        this.customerInfo?.city_id ?? null,
        this.customerInfo?.district_id ?? null,
      ];
    }
    this.basicInfoForm?.get('destination_country')?.setValue(destination_country);

    this.basicInfoForm
      ?.get('destination_country_id')
      ?.setValue(destination_country ? destination_country[0] : null, { emitViewToModelChange: false });
    this.basicInfoForm
      ?.get('destination_province_id')
      ?.setValue(destination_country ? destination_country[1] : null, { emitViewToModelChange: false });
    this.basicInfoForm
      ?.get('destination_city_id')
      ?.setValue(destination_country ? destination_country[2] : null, { emitViewToModelChange: false });
    this.basicInfoForm
      ?.get('destination_distinct_id')
      ?.setValue(destination_country ? destination_country[3] : null, { emitViewToModelChange: false });
    // 目的港
    this.basicInfoForm?.get('destination_port_name')?.setValue(this.customerInfo?.receiver_port ?? null, { emitViewToModelChange: false });
    // 联系人
    this.basicInfoForm?.get('contact_name')?.setValue(this.customerInfo?.contact_name ?? null, { emitViewToModelChange: false });
    // 联系电话
    this.basicInfoForm?.get('contact_phone')?.setValue(this.customerInfo?.contact_phone ?? null, { emitViewToModelChange: false });
    // 收款账户
    this.basicInfoForm?.get('bank_account')?.setValue(this.customerInfo?.bank_account ?? null, { emitViewToModelChange: false });
    // 开户行
    this.basicInfoForm?.get('deposit_bank')?.setValue(this.customerInfo?.deposit_bank ?? null, { emitViewToModelChange: false });
    // 详细地址
    this.basicInfoForm?.get('address')?.setValue(this.customerInfo?.address ?? null, { emitViewToModelChange: false });
  }

  // 合同金额: 订单信息中应收款一列的总和
  calculateContractAmount(orderItems: any[] = []) {
    const colorsArr: any[] =
      orderItems.map((item: any) => {
        return [...item.colors];
      })[0] ?? [];
    const contract_amount = colorsArr?.reduce((prev: any, curr: any) => {
      return prev + Number(curr?.receivables ?? 0);
    }, 0);
    this.basicInfoForm?.get('contract_amount')?.setValue(contract_amount);
    this.calculatRmbAmount();
  }

  // 人民币金额 = 合同金额 / 汇率（注：结果保留2位）
  calculatRmbAmount() {
    const rate = this.basicInfoForm?.get('rate')?.value;
    const contract_amount = this.basicInfoForm?.get('contract_amount')?.value;

    // 当汇率为0时，人民币金额设置为null（显示为"-"）
    if (Number(rate) === 0 || rate === null || rate === undefined) {
      this.basicInfoForm?.get('rmb_contract_amount')?.setValue(null);
      return;
    }

    const rmb_contract_amount = this._flcUtilService.accDivPlus(Number(contract_amount ?? 0), Number(rate));
    this.basicInfoForm?.get('rmb_contract_amount')?.setValue(this._flcUtilService.toFixed(rmb_contract_amount, 2));
  }

  // 表单是否有效
  async isFormValid(): Promise<boolean> {
    if (await this.flcValidatorService.formIsAsyncInvalid(this.basicInfoForm)) {
      this.flcValidatorService.markFormDirty(this.basicInfoForm);
      return false;
    }
    return true;
  }

  // 表单值
  getPayload() {
    const data = this.basicInfoForm.getRawValue();

    // 当汇率为0时，人民币金额不传给后端
    let rmb_contract_amount = null;
    if (Number(data?.rate) !== 0 && data?.rmb_contract_amount) {
      rmb_contract_amount = data.rmb_contract_amount.toString();
    }

    return {
      ...data,
      receive_money_limit: data.receive_money_limit ? Number(format(data.receive_money_limit, 'T')) : null,
      sales_date: data.sales_date ? Number(format(data.sales_date, 'T')) : null,
      contract_date: data.contract_date ? Number(format(data.contract_date, 'T')) : null,
      contract_amount: data?.contract_amount ? data.contract_amount.toString() : null,
      rmb_contract_amount: rmb_contract_amount,
      rate: data?.rate ? data.rate.toString() : null,
    };
  }
}
