{"customRecordCommonFiled": {"客户编码": "Customer Code", "客户全称": "Full name of Customer", "客户简称": "Customer Abbreviation", "货币单位": "Monetary unit", "收货国": "Receiving country", "创建人": "Creator", "创建时间": "Creation time", "客户": "Customer", "客户档案": "Customer Profile", "状态": "Status", "统一社会信用代码": "Unified Social Credit Code", "直接汇率": "Direct rate", "直接汇率计算公式": "Direct exchange rate = Foreign currency price/RMB price", "运输方式": "Mode of transport", "收货港": "Port of receipt", "详细地址": "Detailed address", "联系人": "Contact", "联系电话": "Contact number", "开户行": "Opening bank", "银行账号": "Bank account", "备注": "Remark", "系统账号": "System Account", "基础档案详情": "Basic Archive Details", "基础档案创建": "Basic Archive Create", "额度详情": "Credit details", "基础档案编辑": "Basic Archive Edit", "确定启用所选数据": "Sure to enable selected data?", "确定禁用所选数据": "Sure to disable selected data?", "确定删除所选数据": "Sure to delete selected data?", "启用": "enable", "禁用": "disable", "请选择": "Pls select", "请输入": "Pls enter", "额度": "Limit"}}