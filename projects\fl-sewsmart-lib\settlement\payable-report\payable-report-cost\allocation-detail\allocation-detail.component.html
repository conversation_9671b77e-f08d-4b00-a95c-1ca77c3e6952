<flss-payable-report-detail-header
  [editMode]="editMode"
  [detailInfo]="detailInfo"
  (onAction)="onAction($event)"></flss-payable-report-detail-header>

<flss-payable-report-baseinfo #baseinfoComponent [editMode]="editMode" [detailInfo]="detailInfo"></flss-payable-report-baseinfo>

<!-- 分摊金额 -->
<flss-payable-report-detail-allocated-amount
  #allocatedAmountComponent
  [editMode]="editMode"
  [detailInfo]="detailInfo"
  (totalAmountChange)="onTotalAmountChange($event)">
</flss-payable-report-detail-allocated-amount>

<!-- 分摊明细 -->
<flss-allocation-detail-drawer
  #allocationDetailComponent
  [editMode]="editMode"
  [detailInfo]="detailInfo"
  [totalAllocationAmount]="totalAllocationAmount">
</flss-allocation-detail-drawer>
