import { Component, ElementRef, On<PERSON><PERSON>roy, OnInit, ViewChild } from '@angular/core';
import { BroadcastService, FlcModalService, FlcTableComponent, resizable } from 'fl-common-lib';
import { NzMessageService } from 'ng-zorro-antd/message';
import { debounceTime, finalize, Subscription } from 'rxjs';
import { BatchActionEnum, ButtonTypeEnum, DefaultTableHeaders, SearchOptions } from '../custom-record.config';
import { CustomRecordService } from '../custom-record.service';
import { NzModalService } from 'ng-zorro-antd/modal';
import { endOfDay, format, startOfDay } from 'date-fns';
import { Router } from '@angular/router';
import { CurrencyDetailModalComponent } from '../components/currenncy-detail-modal/currenncy-detail-modal.component';
@Component({
  selector: 'flss-custom-record-list',
  templateUrl: './custom-record-list.component.html',
  styleUrls: ['./custom-record-list.component.scss'],
  providers: [],
})
@resizable()
export class customRecordListComponent implements OnInit, OnDestroy {
  @ViewChild('customTable') customTable!: FlcTableComponent;
  @ViewChild('searchBar') searchBarRef!: ElementRef;

  translateName = 'customRecordCommonFiled.';

  searchOptionFetchUrl = this._service.serviceUrl + '/customer/operation';

  searchData: any = {
    code: null,
    short_name: null,
    receiver_country_name: null,
    status: null,
    gen_user_name: null,
    gen_time: null,
  };

  searchOptions = SearchOptions;

  // 表格配置
  tableConfig = {
    hasAction: true,
    detailBtn: false,
    hasCheckbox: true,
    dataList: [] as Array<any>,
    count: 20,
    pageSize: 20,
    pageIndex: 1,
    order_by: [],
    height: 200,
    loading: false,
    uniqueId: 'id',
    translateName: this.translateName,
    tableName: 'customRecordTable',
    actionWidth: '160px',
    inactiveId: 'status',
    inactiveValue: 0,
  };
  tableHeader: any[] = JSON.parse(JSON.stringify(DefaultTableHeaders));
  selectedCount = 0;
  selectedIds = [];
  _searchDataInitState: any;
  permissionMap: any = {};
  actionConfig: any = {
    uaType: 'basic-archive',
    uaKey: 'custom-record',
    modeType: 'custom-record',
    popoverTextConfigType: '客户',
  };

  // 额外的操作按钮配置
  extraActionConfig = [
    {
      icon: 'icon-quota', // 额度图标，可以根据实际图标库调整
      className: 'primary',
      key: 'quota',
      popoverTrigger: null,
      text: '额度', // 按钮文本
    },
  ];
  translateSubject?: Subscription; // 国际化监听
  broadCastSubscription?: Subscription;

  constructor(
    private _service: CustomRecordService,
    private _router: Router,
    private _flcModalService: FlcModalService,
    private broadcast: BroadcastService,
    private _message: NzMessageService,
    private _modalService: NzModalService
  ) {}

  ngOnInit() {
    (this as any).addResizePageListener();
    // 权限
    const permissions = this._service.initUserActions();
    if (permissions) {
      this.permissionMap = {
        create: permissions.includes(this._service.permissionKey + 'create'),
      };
    }
    this._searchDataInitState = JSON.parse(JSON.stringify(this.searchData));
    this.getOptions();
    this._getDataList();

    this.translateSubject = this._service.translateEventEmitter.subscribe(() => {
      this.resizePage();
    });
    this.broadCastSubscription = this.broadcast.eavesdrop((event) => {
      if (event.channel === '/basic-archive/custom-record/list') {
        this._getDataList();
      }
    });
  }

  ngAfterViewInit(): void {
    this.resizePage();
  }

  resizePage() {
    setTimeout(() => {
      const searchBarHeight = this.searchBarRef.nativeElement.clientHeight ?? 32;
      let _height = window.innerHeight - searchBarHeight - 150;
      if (_height < 200) {
        _height = 200;
      }
      this.tableConfig = { ...this.tableConfig, height: _height };
    }, 0);
  }

  ngOnDestroy(): void {
    (this as any).removeResizePageListener();
    this.translateSubject?.unsubscribe();
    this.broadCastSubscription?.unsubscribe();
  }

  getOptions() {
    this._service.customerListOptions().subscribe((res) => {
      if (res) {
        this.searchOptions.forEach((item: any) => {
          if (item.type === 'local-select') {
            item.options = res.data[item.relate_key];
          }
        });
      }
    });
  }

  onChangeDataList() {
    this._getDataList();
  }

  onReset() {
    this.searchData = JSON.parse(JSON.stringify(this._searchDataInitState));
    this.tableConfig = {
      ...this.tableConfig,
      pageSize: 20,
    };
    this.customTable.clearAllSelected();
    this._getDataList();
  }

  // 选中的数量
  onSelectedCount(e: any) {
    this.selectedCount = e.count;
    this.selectedIds = e.list.map((item: any) => item.id);
  }

  onIndexChange(e: number) {
    this.tableConfig = { ...this.tableConfig, pageIndex: e };
    this._getDataList();
  }

  onSizeChanges(e: number) {
    this.tableConfig = { ...this.tableConfig, pageSize: e };
    this._getDataList();
  }

  _getDataList() {
    let startTime = null;
    let endTime = null;
    if (this.searchData?.gen_time?.length) {
      startTime = format(startOfDay(this.searchData?.gen_time[0] ?? null), 'T');
      endTime = format(endOfDay(this.searchData?.gen_time[1] ?? null), 'T');
    }

    const data = {
      gen_time_start: startTime,
      gen_time_end: endTime,
      ...this.searchData,
      page: this.tableConfig.pageIndex,
      page_size: this.tableConfig.pageSize,
    };
    this.tableConfig.loading = true;
    this._service
      .getCustomerList(data)
      .pipe(
        finalize(() => {
          this.tableConfig = { ...this.tableConfig, loading: false };
        })
      )
      .subscribe((res) => {
        if (res) {
          this.tableConfig.dataList = res.data?.list ?? [];
          this.tableConfig.dataList.forEach((item: any) => {
            item.status = item.status === 1 ? true : false;
          });
          this.tableConfig.count = res.data.total;
          this.tableConfig = { ...this.tableConfig };
        }
      });
  }

  onFold() {
    this.resizePage();
  }

  onAdd() {
    this._router.navigate(['basic-archive/custom-record/list/new']);
    // this._router.navigate([`basic-archive/custom-record/list/${-1}`]);
  }

  // 操作列
  onAction(e: { key: string; data: any }) {
    switch (e.key) {
      case 'quota':
        this._onQuota(e.data);
        break;
      case ButtonTypeEnum.delete:
        this._onDelete(false, e.data);
        break;
      default:
        break;
    }
  }

  onDetail(id: number) {
    // this.lightId = id;
    this._router.navigate([`basic-archive/custom-record/list/${id}`]);
  }

  // 额度操作
  _onQuota(data: any) {
    console.log('打开额度弹窗', data);

    const _modal = this._modalService.create({
      nzTitle: '币种额度详情',
      nzFooter: null,
      nzWidth: '1100px',
      nzMaskClosable: false,
      nzKeyboard: false,
      nzBodyStyle: { padding: '0px' },
      nzContent: CurrencyDetailModalComponent,
    });

    // 设置弹窗组件的输入参数
    if (_modal.componentInstance) {
      _modal.componentInstance.customerId = data.id;
      _modal.componentInstance.customerName = data.name || data.short_name;
      _modal.componentInstance.paymentQuotas = data.payment_quotas || [];
    }

    // 监听弹窗关闭后的操作
    _modal.afterClose.subscribe((result) => {
      if (result && result.refresh) {
        // 如果弹窗返回需要刷新的标识，则刷新列表数据
        this.onChangeDataList();
      }
    });
  }

  // 单个禁用启用
  onToggleSingleStatus(e: { status: boolean; data: any }): void {
    const model = {
      ids: [e.data.id],
      status: e.status ? 1 : 2,
    };
    this._changeStatus(model, false);
  }

  // 改变状态
  private _changeStatus(model: any, isBatch: boolean): void {
    this._service
      .setCustomerStatus(model)
      .pipe(
        debounceTime(500),
        finalize(() => {
          // 批量：清空选择项
          if (isBatch) {
            this.customTable.clearAllSelected();
          }
        })
      )
      .subscribe((res) => {
        if (res.code === 200) {
          this._message.success(this._service.translateValue('archiveMsg.' + (model?.status === 2 ? '禁用成功' : '启用成功')));
          this._getDataList();
        }
      });
  }

  // 批量操作
  onBatchAction(key: string): void {
    switch (key) {
      case BatchActionEnum.enabled:
        this._onBatchChangeStatus(true);
        break;
      case BatchActionEnum.disabled:
        this._onBatchChangeStatus(false);
        break;
      case BatchActionEnum.delete:
        this._onDelete(true);
        break;
      default:
        break;
    }
  }

  // 批量启用禁用
  _onBatchChangeStatus(isEnable: boolean): void {
    const ref = this._flcModalService.confirmCancel({
      title: this._service.translateValue('archiveMsg.' + (isEnable ? '批量启用' : '批量禁用')),
      content: this._service.translateValue(this.translateName + (isEnable ? '确定启用所选数据' : '确定禁用所选数据')),
      contentCenter: true,
      btnCenter: true,
      okText: this._service.translateValue('flss.btn.ok'),
      cancelText: this._service.translateValue('flss.btn.cancel'),
    });
    ref.afterClose.subscribe((res: any) => {
      if (res) {
        const model = {
          ids: this.selectedIds,
          status: isEnable ? 1 : 2,
        };
        this._changeStatus(model, true);
      }
    });
  }

  // 删除
  _onDelete(isBatch: boolean, item?: any): void {
    const _deleteFn = () => {
      this._service
        .customerDelete({ ids: isBatch ? this.selectedIds : [item.id] })
        .pipe(
          finalize(() => {
            // 批量，清空所有的选择项，单个：更新当前所有选中项
            if (isBatch) {
              this.customTable.clearAllSelected();
            }
          })
        )
        .subscribe((res: any) => {
          if (res.code === 200) {
            this._message.success(this._service.translateValue('flss.success.delete'));
            this._getDataList();
            // 批量，清空所有的选择项，单个：更新当前所有选中项
            if (!isBatch) {
              this.customTable.onItemChecked(item.id, false);
            }
          }
        });
    };
    if (isBatch) {
      const ref = this._flcModalService.confirmCancel({
        title: this._service.translateValue('archiveMsg.批量删除'),
        content: this._service.translateValue(this.translateName + '确定删除所选数据'),
        contentCenter: true,
        btnCenter: true,
        okText: this._service.translateValue('flss.btn.ok'),
        cancelText: this._service.translateValue('flss.btn.cancel'),
      });
      ref.afterClose.subscribe((res) => {
        if (res) {
          _deleteFn();
        }
      });
    } else {
      _deleteFn();
    }
  }
}
