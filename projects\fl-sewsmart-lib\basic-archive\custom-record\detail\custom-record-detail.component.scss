.custom-container {
  display: flex;
  flex-direction: column;

  .info-label {
    width: 120px;
    margin-right: 10px;
  }

  .info-label-en {
    width: 180px;
    margin-right: 10px;
  }

  .info-control {
    flex: 1;
  }

  ::ng-deep {
    .header-title-top {
      font-weight: 500;
      color: #515661;
      font-size: 16px;
    }
    .header-btn {
      gap: 8px;
    }
  }

  .inner-container {
    background: #fff;
    border-radius: 4px;
    max-height: calc(100vh - 112px);
    padding: 16px;
    overflow-y: auto;
    overflow-x: hidden;
  }

  .currency-quota-container {
    .form-item {
      margin-bottom: 16px;
    }

    ::ng-deep {
      .ant-table-tbody > tr > td {
        padding: 12px 16px;
      }

      .ant-input-number {
        width: 100%;
      }
    }
  }
}

:host ::ng-deep {
  .ant-input-suffix {
    color: #97999c;
    font-size: 12px;
  }
}
