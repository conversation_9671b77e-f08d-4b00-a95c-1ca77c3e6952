import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { FlcModalService, FlcValidatorService, resizable } from 'fl-common-lib';
import { NzNotificationService } from 'ng-zorro-antd/notification';
import { NzMessageService } from 'ng-zorro-antd/message';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ScmService } from '../scm.service';
import { format } from 'date-fns';
import { ElanStyleTableComponent } from '../components/elan-style-table/elan-style-table.component';
import { ElanProcurementTableComponent } from '../components/elan-procurement-table/elan-procurement-table.component';
import { ElanOrderTableComponent } from '../components/elan-order-table/elan-order-table.component';
import { INBOUND_TYPE } from '../../product-in-stock.config';

@Component({
  selector: 'flss-detail',
  templateUrl: './detail.component.html',
  styleUrls: ['./detail.component.scss'],
})
@resizable()
export class DetailComponent implements OnInit {
  @ViewChild('head') head!: ElementRef;
  @ViewChild('orderTable') orderTable!: ElanProcurementTableComponent | ElanStyleTableComponent | ElanOrderTableComponent;
  tableHeight!: number;
  id!: number | string;
  basicForm!: FormGroup;
  oldType: any;
  oldWarehouseId: any;
  originalProductTypes: any[] = []; // 保存原始的产品类型选项
  editInfo = [
    { label: '入库单号', key: 'code', type: 'input', required: true, visible: true, nzSpan: 8, labelSpan: 6, valueSpan: 18 },
    {
      label: '入库类型',
      key: 'inbound_type',
      listKey: 'inbound_types',
      type: 'select',
      required: true,
      visible: true,
      nzSpan: 8,
      labelSpan: 6,
      valueSpan: 18,
    },
    {
      label: '产品类型',
      key: 'product_type',
      listKey: 'product_types',
      type: 'select',
      required: true,
      visible: true,
      nzSpan: 8,
      labelSpan: 6,
      valueSpan: 18,
    },
    {
      label: '入库仓库',
      key: 'warehouse_id',
      listKey: 'warehouse_ids',
      type: 'select',
      required: true,
      visible: true,
      nzSpan: 8,
      labelSpan: 6,
      valueSpan: 18,
    },
    { label: '加工厂', key: 'factory_name', type: 'text', required: false, visible: false, nzSpan: 8, labelSpan: 6, valueSpan: 18 },
    { label: '客户', key: 'customer_name', type: 'text', required: false, visible: false, nzSpan: 8, labelSpan: 6, valueSpan: 18 },
    { label: '备注', key: 'remark', type: 'textarea', maxLength: 255, visible: true, nzSpan: 16, labelSpan: 3, valueSpan: 21 },
  ];
  readInfo = [
    { label: '入库单号', key: 'code' },
    { label: '入库类型', key: 'inbound_type_name' },
    { label: '入库仓库', key: 'warehouse_name' },
    { label: '入库数量', key: 'all_inbound_qty' },
    { label: '入库成本', key: 'inbound_cost' },
    { label: '关联单据', key: 'doc_codes' },
    { label: '产品类型', key: 'product_type_name' },
    { label: '入库时间', key: 'inbound_time' },
    { label: '创建时间', key: 'gen_time' },
    { label: '创建人', key: 'gen_user_name' },
    { label: '备注', key: 'remark' },
  ];
  optionsList: any = {
    inbound_types: [],
    warehouse_ids: [],
  };
  isEdit!: boolean;
  details: any = {
    lines: [],
    inbound_status: 0,
    gen_terminal: '',
    inbound_status_name: '',
  };
  isCanLeave = false;
  dataListLength: any = this.details.lines.length;
  afterChangeH = false;
  userActions: any = [];

  INBOUND_TYPE = INBOUND_TYPE;
  constructor(
    private _router: Router,
    private _fb: FormBuilder,
    private _validator: FlcValidatorService,
    private _route: ActivatedRoute,
    private _service: ScmService,
    private _flcModal: FlcModalService,
    private _activeRoute: ActivatedRoute,
    private notification: NzNotificationService,
    private message: NzMessageService
  ) {}

  ngOnInit(): void {
    this.id = this._route.snapshot.paramMap.get('id') || 'new';
    this.userActions = this._service.getUserActions();
    if (this.id === 'new') {
      this.isEdit = true;
      this.initBaseForm();
      this.getCreateOptions();
      this.getCode();
    } else {
      this.getCreateOptions();
      this.id = Number(this.id);
      this.getDetails(this.id);
    }
    (this as any).addResizePageListener();
  }
  ngOnDestroy(): void {
    (this as any).removeResizePageListener();
  }
  getDataListLength(e: any) {
    this.dataListLength = e;
  }

  getAllInboundQty(e: any) {
    this.basicForm.get('all_inbound_qty')?.setValue(e);
  }

  getFactory(e: any) {
    this.basicForm.get('factory_id')?.setValue(e.factory_id);
    this.basicForm.get('factory_name')?.setValue(e.factory_name);
  }

  getCustomer(e: any) {
    this.basicForm.get('customer_id')?.setValue(e.customer_id);
    this.basicForm.get('customer_name')?.setValue(e.customer_name);
  }

  getProductType(e: any) {
    // 更新产品类型值但不触发验证
    this.basicForm.get('product_type')?.setValue(e, { emitEvent: false });
  }

  ngAfterViewInit() {
    this.resizePage();
  }
  getInboundCost(e: any) {
    this.basicForm.get('inbound_cost')?.setValue(e);
  }
  getCode() {
    this._service.getUniqueCode().subscribe((res: any) => {
      if (res.code === 200) {
        this.details.code = res.data.code;
        this.basicForm?.get('code')?.setValue(this.details.code);
        this.basicForm?.get('code')?.setAsyncValidators(this._service.uniqueValidator(res.data.code, null));
      }
    });
  }
  getCreateOptions() {
    this._service.getCreateOptions().subscribe((res: any) => {
      if (res.code === 200) {
        this.optionsList = { ...res.data };

        // 保存原始的产品类型选项
        if (this.optionsList?.product_types) {
          this.originalProductTypes = [...this.optionsList.product_types];
        }

        if (!this.basicForm?.get('warehouse_id')?.value) {
          this.basicForm?.get('warehouse_id')?.setValue(this.optionsList?.warehouse_ids[0]?.value || null);
          this.basicForm?.get('warehouse_name')?.setValue(this.optionsList?.warehouse_ids[0]?.label || null);
        }
      }
    });
  }
  getDetails(id: number | string) {
    this._service.getDetails({ id: id }).subscribe((res: any) => {
      if (res.code === 200) {
        this.afterChangeH = false;
        this.details = res.data;
        this.details.lines = res.data.lines;
        this.oldType = res.data.inbound_type;

        if (res.data.inbound_type === INBOUND_TYPE.大货入库) {
          this.details.lines.forEach((con: any) => {
            con.custom_unique_code = `${con.io_code}-${con.bulk_code}-${con.po_code}-${con.factory_name}-${con.stages}-${con.color_name}-${con.spec_code}`;
          });
        }
        if (res.data.inbound_type === INBOUND_TYPE.销退入库) {
          this.details.lines.forEach((con: any) => {
            con.custom_unique_code = `${con.io_code}-${con.po_code}-${con.color_name}-${con.spec_code}`;
          });
        }

        this.initBaseForm(this.details);

        // 根据当前入库类型更新产品类型选项
        if (this.details.inbound_type) {
          this.updateProductTypeOptions(this.details.inbound_type);
        }
        if (this.basicForm?.get('inbound_type')?.value === INBOUND_TYPE.采购入库) {
          res.data.lines.forEach((item: any) => {
            item?.procurement_plans.forEach((plan: any) => {
              plan['task_string'] = '';
              plan['task_ext'].forEach((task: any, i: number) => {
                plan['task_string'] =
                  plan['task_string'] +
                  format(Number(task.due_time), 'yyyy-MM-dd') +
                  '/' +
                  String(task.due_num) +
                  '/' +
                  String(task.logistics_no) +
                  (i === plan['task_ext'].length - 1 ? '' : '、');
              });
              // delete plan.task_ext;
              // plan['task_ext'] = plan['task_string'];
            });
          });
        }
        if (this.basicForm?.get('inbound_type')?.value === INBOUND_TYPE.大货入库) {
          this.readInfo.splice(7, 0, { label: '加工厂', key: 'factory_name' });
        }
        if (this.basicForm?.get('inbound_type')?.value === INBOUND_TYPE.销退入库) {
          this.readInfo.splice(7, 0, { label: '客户', key: 'customer_name' });
        }
        this.basicForm?.get('code')?.setAsyncValidators(this._service.uniqueValidator(res.data.code, res.data.id));
      }
    });
  }
  // 基本信息编码
  changeValue(e: INBOUND_TYPE, key: string) {
    if (key === 'inbound_type') {
      this.updateFormItems(e);
      this.basicForm.get('inbound_type_name')?.setValue(this.optionsList?.inbound_types.find((item: any) => item.value === e)?.label || '');

      // 根据入库类型调整产品类型选项
      this.updateProductTypeOptions(e);
    }

    if (this.dataListLength > 0 && key === 'inbound_type') {
      this.details.lines = this.orderTable.getTableData().dataList;
      this.changeType(e);
    } else if (!this.dataListLength && key === 'inbound_type') {
      this.oldType = e;
    }
    if (this.dataListLength > 0 && key === 'warehouse_id') {
      this.details.lines = this.orderTable.getTableData().dataList;
      this.afterChangeH = false;
      this.changeWareH(e);
    } else if (!this.dataListLength && key === 'warehouse_id') {
      this.oldWarehouseId = e;
      this.basicForm
        .get('warehouse_name')
        ?.setValue(
          this.optionsList.warehouse_ids.find((item: any) => item.value === this.basicForm.get('warehouse_id')?.value)?.label || ''
        );
    }

    if (key === 'product_type') {
      this.basicForm.get('product_type_name')?.setValue(this.optionsList?.product_types.find((item: any) => item.value === e)?.label || '');
    }
  }
  changeWareH(e: any) {
    this._flcModal
      .confirmCancel({
        iconColor: '#FB6401',
        iconClass: 'icon-yiwen',
        content: '确定更换入库仓库',
        subContent: '更换入库仓库后，入库清单数据中的货位会被清空哦～',
        showSubContent: true,
      })
      .afterClose.subscribe((confirm: any) => {
        if (confirm) {
          this.oldWarehouseId = e;
          this.afterChangeH = true;
        } else {
          this.basicForm.get('warehouse_id')?.setValue(this.oldWarehouseId, { emitViewToModelChange: false });
          this.afterChangeH = false;
        }
        this.basicForm
          .get('warehouse_name')
          ?.setValue(
            this.optionsList.warehouse_ids.find((item: any) => item.value === this.basicForm.get('warehouse_id')?.value)?.label || ''
          );
      });
  }
  changeType(e: any) {
    this._flcModal
      .confirmCancel({
        iconColor: '#FB6401',
        iconClass: 'icon-yiwen',
        content: '确定更换入库类型',
        subContent: '更换入库类型后，入库清单数据会被清除哦～',
        showSubContent: true,
      })
      .afterClose.subscribe((confirm: any) => {
        if (confirm) {
          this.oldType = e;
          this.details.lines = [];
          this.basicForm.get('inbound_cost')?.setValue('');
          this.afterChangeH = true;
        } else {
          this.basicForm.get('inbound_type')?.setValue(this.oldType, { emitViewToModelChange: false });
          this.afterChangeH = false;
        }
      });
  }

  updateFormItems(inbound_type: INBOUND_TYPE) {
    /* eslint-disable @typescript-eslint/no-non-null-assertion */
    const factory = this.editInfo.find((item) => item.key === 'factory_name')!;
    const customer = this.editInfo.find((item) => item.key === 'customer_name')!;
    const remark = this.editInfo.find((item) => item.key === 'remark')!;
    // 调整加工厂和客户的显示隐藏
    factory.visible = inbound_type === INBOUND_TYPE.大货入库;
    customer.visible = inbound_type === INBOUND_TYPE.销退入库;

    // 调整备注的宽度
    Object.assign(remark, {
      nzSpan: factory.visible || customer.visible ? 8 : 16,
      labelSpan: factory.visible || customer.visible ? 6 : 3,
      valueSpan: factory.visible || customer.visible ? 18 : 21,
    });

    this.editInfo = [...this.editInfo];
  }

  // 根据入库类型更新产品类型选项
  updateProductTypeOptions(inbound_type: INBOUND_TYPE) {
    if (!this.optionsList?.product_types) return;

    // 保存原始的产品类型选项（如果还没有保存）
    if (!this.originalProductTypes) {
      this.originalProductTypes = [...this.optionsList.product_types];
    }

    let filteredProductTypes: any[] = [];

    if (inbound_type === INBOUND_TYPE.销退入库) {
      // 销退入库：只显示"成品"选项
      filteredProductTypes = this.originalProductTypes.filter((item: any) => item.label === '成品' || item.value === 'finished_product');
    } else if (inbound_type === INBOUND_TYPE.大货入库) {
      // 大货入库：显示"半成品"和"成品"选项
      filteredProductTypes = this.originalProductTypes.filter(
        (item: any) =>
          item.label === '半成品' || item.label === '成品' || item.value === 'semi_finished_product' || item.value === 'finished_product'
      );
    } else {
      // 其他入库类型：显示所有选项
      filteredProductTypes = [...this.originalProductTypes];
    }

    // 更新选项列表
    this.optionsList.product_types = filteredProductTypes;

    // 如果当前选中的产品类型不在新的选项中，清空选择
    const currentProductType = this.basicForm.get('product_type')?.value;
    const isCurrentTypeValid = filteredProductTypes.some((item: any) => item.value === currentProductType);

    if (!isCurrentTypeValid) {
      this.basicForm.get('product_type')?.setValue(null);
      this.basicForm.get('product_type_name')?.setValue('');
    }
  }

  initBaseForm(data?: any) {
    const basicForm = this._fb.group({
      id: [data?.id || null],
      code: [data?.code || '', [Validators.required]],
      product_type: [data?.product_type || null, [Validators.required]],
      product_type_name: [data?.product_type_name || ''],
      warehouse_name: [data?.warehouse_name || ''],
      warehouse_id: [data?.warehouse_id || null, [Validators.required]],
      inbound_type_name: [data?.inbound_type_name || ''],
      inbound_type: [data?.inbound_type || null, [Validators.required]],
      inbound_cost: [data?.inbound_cost || ''],
      remark: [data?.remark || ''],
      gen_time: [data?.gen_time || ''],
      gen_user_name: [data?.gen_user_name || ''],
      inbound_time: [data?.inbound_time || ''],
      doc_codes: [data?.doc_codes || []],
      all_inbound_qty: [null],
      factory_id: [data?.factory_id || null],
      factory_name: [data?.factory_name || ''],
      customer_id: [data?.customer_id || null],
      customer_name: [data?.customer_name || ''],
    });
    this.basicForm = basicForm;
  }
  getInvalid() {
    const isInvalid = this._validator.formIsInvalid(this.basicForm);
    return isInvalid;
  }
  // 基本信息编码结束
  // 调整
  resizePage() {
    setTimeout(() => {
      const otherHeight = this.head?.nativeElement?.clientHeight ?? 32;
      let _height = window.innerHeight - otherHeight - 50 - 16;
      if (_height < 200) {
        _height = 200;
      }
      console.log(_height);
      this.tableHeight = _height;
    }, 200);
  }
  canLeave() {
    return !(this.isEdit && (this.orderTable.tableChange() || this.basicForm?.dirty)) || this.isCanLeave;
  }
  modify() {
    this.isEdit = true;
  }
  cancel() {
    const ref = this._flcModal.confirmCancel({ type: 'confirm-cancel' });
    ref.afterClose.subscribe((res) => {
      if (res) {
        if (this.id === 'new') {
          this.isCanLeave = true;
          this._router.navigate(['/product-inventory/product-in-stock/scm/list']);
        } else {
          this.isCanLeave = true;
          this._router.navigate(['/product-inventory/product-in-stock/scm/list', this.id]);
          (this.basicForm as any) = null;
          this.getDetails(this.id);
          this.isEdit = false;
        }
      }
    });
  }
  save() {
    if (!this.basicForm.get('code')?.value) {
      this.notification.create('error', '入库单号不可为空', '');
      return;
    }
    const data = this.handleData();
    this._service.storeStock(data).subscribe((res: any) => {
      if (res.code === 200) {
        this.message.success('暂存成功');
        this.isCanLeave = true;
        this._router.navigate(['/product-inventory/product-in-stock/scm/list', res.data.id]);
        this.getDetails(res.data.id);
        (this.basicForm as any) = null;
        this.isEdit = true;
        this.id = res.data.id;
      }
    });
  }
  commit() {
    const data = this.handleData();
    if (!data.lines?.length) {
      this.notification.create('error', '请添加入库清单', '');
      return;
    }
    const inVaild = this.getInvalid();
    const inVaildTable = this.orderTable.getInvaild();
    if (inVaild || inVaildTable) {
      this.notification.create('error', '请检查必填项', '');
      return;
    }
    if (this.basicForm.get('inbound_type')?.value === INBOUND_TYPE.采购入库) {
      const index = data.lines.findIndex((item: any) => {
        let locQty = 0;
        let ordersQty = 0;
        item.location.forEach((loc: any) => {
          locQty = locQty + loc.inbound_qty;
        });
        const list = this.basicForm.get('inbound_type')?.value === 3 ? 'orders' : 'procurement_plans';
        item[list].forEach((order: any) => {
          ordersQty = ordersQty + order.inbound_qty;
        });
        return locQty !== ordersQty;
      });
      if (index > -1) {
        this.notification.create('error', `第${index + 1}行所有货位的入库数量之和不等于每个采购单的入库数量之和, 请修改`, '');
        return;
      }
    }

    this._service.commitStock(data).subscribe((res: any) => {
      if (res.code === 200) {
        this.message.success('确认入库成功');
        this.isCanLeave = true;
        this._router.navigate(['/product-inventory/product-in-stock/scm/list', res.data.id]);
        this.getDetails(res.data.id);
        (this.basicForm as any) = null;
        this.isEdit = false;
        this.id = res.data.id;
      }
    });
  }
  handleData() {
    const delete_location_ids: any = this.orderTable?.getTableData().delete_location_ids;
    const delete_line_ids: any = this.orderTable?.getTableData().delete_line_ids;
    const delete_procurement_plan_ids: any = this.orderTable?.getTableData()?.['delete_procurement_plan_ids'] || [];
    const delete_order_ids: any = this.orderTable?.getTableData()?.['delete_order_ids'] || [];
    const lines = this.orderTable?.getTableData().dataList;
    if (this.basicForm?.get('inbound_type')?.value === INBOUND_TYPE.采购入库) {
      const list = 'procurement_plans';
      lines.forEach((item: any) => {
        item?.[list].forEach((plan: any) => {
          delete plan['inboundForm'];
        });
      });
    }
    const data = {
      ...this.basicForm.getRawValue(),
      version: this.details.version || null,
      lines: lines,
      delete_line_ids,
      delete_location_ids,
      delete_order_ids,
      delete_procurement_plan_ids,
    };
    return data;
  }
}
