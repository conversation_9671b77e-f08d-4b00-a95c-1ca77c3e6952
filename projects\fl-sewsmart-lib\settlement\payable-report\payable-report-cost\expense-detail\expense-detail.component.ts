import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Location } from '@angular/common';
import { PayableReportService } from '../../payable-report.service';
import { ExpenseDetailService } from './expense-detail.service';
import { NzMessageService } from 'ng-zorro-antd/message';
import { FormGroup, FormBuilder, Validators, FormArray, AbstractControl } from '@angular/forms';
import { OptionConfig, StatusStyleConfig, TableHeader } from './models/expense-detail.config';
import { finalize, forkJoin, of } from 'rxjs';
import { FlcModalService, FlcTableHelperService, FlcValidatorService, BroadcastService, BroadcastDefualtKey } from 'fl-common-lib';
import { ExpenseDetailOperationButtonEnum, ExpenseDetailStatusEnum } from './models/expense-detail.enum.enum';
import { format, getTime } from 'date-fns';
import { TranslateService } from '@ngx-translate/core';
import { NzNotificationService } from 'ng-zorro-antd/notification';
import { NzModalService } from 'ng-zorro-antd/modal';
import { ReturnModelComponent } from '../../../components/return-model/return-model.component';

@Component({
  selector: 'flss-expense-detail',
  templateUrl: './expense-detail.component.html',
  styleUrls: ['./expense-detail.component.scss'],
  providers: [ExpenseDetailService, PayableReportService],
})
export class ExpenseDetailComponent implements OnInit {
  // 需要查询的id
  _id = '';
  // 是否是编辑状态
  isEdit = false;

  // 颜色配置
  StatusStyle = StatusStyleConfig;
  // 操作类型
  ActionType = ExpenseDetailOperationButtonEnum;
  ExpenseDetailStatus = ExpenseDetailStatusEnum;
  // 支出表单
  expenditureForm: FormGroup = this.initExpenditureForm();
  // 支出下拉选项配置
  optionConfig = OptionConfig;
  // 获取下拉选项数据
  expenseDetailOption: any = null;

  // 批量添加弹出框状态
  isPopoverVisible = false;
  // 添加行数
  addRows = 1;

  // 表头渲染
  tableHeader = TableHeader;

  // 详情数据
  detailData = null;

  actionMap = {
    hasSave: false,
    hasAudit: false,
    hasReject: false,
    hasCancel: false,
  };

  constructor(
    private _fb: FormBuilder,
    private _route: ActivatedRoute,
    private _router: Router,
    private _flcModal: FlcModalService,
    private _msg: NzMessageService,
    private _service: ExpenseDetailService,
    private _translateService: TranslateService,
    private _notice: NzNotificationService,
    private _flValidatorService: FlcValidatorService,
    private _payableReportService: PayableReportService,
    private _modalService: NzModalService,
    private _broadcast: BroadcastService
  ) {}

  ngOnInit(): void {
    this._id = this._route.snapshot.paramMap.get('id') ?? '';

    const userActions = this._payableReportService.getUserActions() || [];
    this.actionMap = {
      hasSave: userActions.includes('settlement:expense-management-create'),
      hasAudit: userActions.includes('settlement:expense-management-audit'),
      hasReject: userActions.includes('settlement:expense-management-audit'),
      hasCancel: userActions.includes('settlement:expense-management-create'),
    };

    this.getALLOption();
    if (this._id === 'add') {
      this.isEdit = true;
      this.getCode();
      this._broadcast.yell({
        channel: BroadcastDefualtKey.changeTabTitle,
        message: {
          targetUrl: `${window.location.pathname}`,
          title: '费用支出 - 新建',
        },
      });
    } else {
      this.getDetail();
      this._broadcast.yell({
        channel: BroadcastDefualtKey.changeTabTitle,
        message: {
          targetUrl: `${window.location.pathname}`,
          title: '费用支出 - 详情',
        },
      });
    }
  }

  // 获取时间
  getOccurredAt(value: number[] | null) {
    if (value) {
      return `${format(value?.[0], 'yyyy-MM-dd')} - ${format(value?.[1], 'yyyy-MM-dd')}`;
    }
    return null;
  }
  // 获取单据编号
  private getCode() {
    this._service.getCode().subscribe((res) => {
      if (res.code) {
        this.expenditureForm?.get('code')?.setValue(res.data?.code ?? '', { emitViewToModelChange: false });
      }
    });
  }

  // 获取部门下拉数据
  private getALLOption() {
    forkJoin([this._service.getBasicOption(), this._service.getOption()]).subscribe(([basicOptionRes, optionRes]) => {
      let combinedOptions: any = {};

      if (basicOptionRes.code == 200) {
        console.log(basicOptionRes.data, '123456');

        combinedOptions.departments = this.handleDepartmentOption(basicOptionRes.data.children, []);
      }

      if (optionRes.code == 200) {
        combinedOptions = { ...optionRes.data, ...combinedOptions };
      }

      this.expenseDetailOption = combinedOptions;
    });
  }
  // 处理下拉数据
  private handleDepartmentOption(data: any[], option: any[]) {
    data.forEach((item) => {
      // if (!item.is_relation && item.key !== -1) {
      if (!item.is_relation) {
        option.push({
          label: item.title,
          value: item.key,
        });
      }
      item.children?.length && this.handleDepartmentOption(item.children, option);
    });
    return option;
  }

  // 获取详情
  private getDetail() {
    this._service?.getDetail(this._id).subscribe((res) => {
      if (res.code == 200) {
        this.detailData = res?.data;
        const cloneData = res?.data;
        cloneData.occurred_at = res?.data?.occurred_at_start ? [res?.data?.occurred_at_start, res?.data?.occurred_at_end] : null;
        delete cloneData.occurred_at_start;
        delete cloneData.occurred_at_end;
        this.expenditureForm = this.initExpenditureForm(res?.data);
      }
    });
  }

  // 初始化数据
  private initExpenditureForm(data?: any) {
    return this._fb.group({
      id: [data?.id ?? null], // 主键ID（编辑时必填）
      code: [data?.code ?? null, [Validators.required, Validators.maxLength(20)]], // 单据编号
      department_id: [data?.department_id ?? "", [Validators.required]], // 部门ID
      occurred_at: [data?.occurred_at ?? null, [Validators.required]], // 发生日期
      payment_method_id: [data?.payment_method_id ?? "", [Validators.required]], // 付款方式ID
      bank_account_id: [data?.bank_account_id ?? "", [Validators.required]], // 银行账户ID
      version: [data?.version ?? null],
      lines: this.initLines(data?.lines),
    });
  }

  // 初始化lines
  private initLines(lines?: any[]) {
    if (!lines?.length) lines = [{}];
    const _fb = this._fb.array([]);
    lines?.forEach((item: any) => {
      _fb.push(this.initLineItem(item));
    });
    return _fb;
  }
  // 初始化Lines中的每一项
  private initLineItem(data?: any) {
    const _fb = this._fb?.group({
      id: [data?.id ?? null], // 明细ID
      type_name: [data?.type_name ?? null], // 类型名称
      classification: [data?.classification ?? null], // 分类
      project_one: [data?.project_one ?? null], // 一级项目名称
      project_two: [data?.project_two ?? null], // 二级项目名称
      project_three: [data?.project_three ?? null], // 三级项目名称
      project_code: [data?.project_code ?? null], // 项目编码
      amount: [data?.amount ?? null, Validators.required], // 金额
      is_shared: [data?.is_shared ?? true], // 是否分摊
      customer_id: [data?.customer_id ?? null], // 客户ID
      customer_name: [data?.customer_name ?? null], // 客户名称
      order_uuid: [data?.order_uuid ?? null], // 订单UUID
      order_code: [data?.order_code ?? null], // 订单编号
      style_code: [data?.style_code ?? null], // 款号
    });
    return _fb;
  }

  // 批量添加
  batchAddition() {
    if (!this.addRows) {
      this._msg.error('请输入添加行数！');
      return;
    }
    this.isPopoverVisible = false;
    for (let index = 0; index < this.addRows; index++) {
      this.linesFormArray.push(this.initLineItem());
    }
  }
  // 手动关闭
  closePopover() {
    this.isPopoverVisible = false;
  }
  // 监听关闭
  popoverVisibleChange(value: any) {
    if (!value) {
      this.addRows = 1;
    }
  }

  // 获取formArray
  get linesFormArray() {
    return this.expenditureForm?.get('lines') as FormArray;
  }

  // 订单方面的操作
  orderChange(values: any, control: AbstractControl, key: string) {
    const selectLine = values?.selectLine;
    switch (key) {
      case 'order_code':
        control?.get('order_uuid')?.setValue(selectLine?.value ?? null);
        control?.get(key)?.setValue(selectLine?.label ?? null);
        break;
      case 'customer_id':
        control?.get('customer_name')?.setValue(selectLine?.label ?? null);
        control?.get(key)?.setValue(selectLine?.value ?? null);
        break;
      default:
        control?.get(key)?.setValue(selectLine?.value ?? null);
        break;
    }
  }

  // 获取请求的参数
  getPayLoad(key: any, control: AbstractControl) {
    const queryKey = ['project_code', 'type_name', 'classification', 'project_one', 'project_two', 'project_three'];
    const payload: any = {};
    queryKey?.forEach((item) => {
      if (item == key) {
        payload[key] = null;
      } else {
        payload[item] = control.get(item)?.value;
      }
    });
    return payload;
  }

  // 类型/分类/项目等等改变的时候
  onExpenseTypeChange(value: any, control: AbstractControl, key: string) {
    if (!value?.selectLine) {
      control?.get(key)?.setValue(null, { emitViewToModelChange: false });
      return;
    }
    const payload = this.getPayLoad('', control);
    this._service
      ?.getExpenserTypeOption({
        ...payload,
        limit: 300,
        page: 1,
      })
      .subscribe((res: any) => {
        if (res.code == 200) {
          const options = res.data;
          for (const optionKey in options) {
            if (options?.[optionKey]?.length == 1) {
              control?.get(optionKey)?.setValue(options?.[optionKey]?.[0]?.value, { emitViewToModelChange: false });
            }
          }
        }
      });
  }

  // 操作按钮
  onAction(type: ExpenseDetailOperationButtonEnum) {
    switch (type) {
      case ExpenseDetailOperationButtonEnum.back:
        this.onBack();
        break;
      case ExpenseDetailOperationButtonEnum.cancel:
        if (this._id === 'add') {
          this.onBack();
          return;
        }
        const ref = this._flcModal.confirmCancel({ type: 'confirm-cancel' });
        ref.afterClose.subscribe((res) => {
          if (res) {
            this.expenditureForm = this.initExpenditureForm(this.detailData);
            this.isEdit = false;
          }
        });
        break;
      case ExpenseDetailOperationButtonEnum.undo:
        // 撤销
        this.onBackModify('undo');
        break;
      case ExpenseDetailOperationButtonEnum.edit:
        this.isEdit = true;
        break;
      case ExpenseDetailOperationButtonEnum.save:
        this.onSave();
        break;
      case ExpenseDetailOperationButtonEnum.commit:
        this.onCommit();
        break;
      case ExpenseDetailOperationButtonEnum.backModify:
        // 退回修改
        this.onBackModify('modify');
        break;
      case ExpenseDetailOperationButtonEnum.approved:
        // 审核通过
        this.onApproved();
        break;
    }
  }

  // 删除行
  onDelete(index: number) {
    this.linesFormArray.removeAt(index);
  }
  // 新增行
  onAdd(index: number) {
    this.linesFormArray.insert(index + 1, this.initLineItem());
  }

  // 返回
  private onBack() {
    this._router.navigate(['/settlement/payable-report/cost']);
  }
  // 暂存
  private onSave() {
    const codeControl = this.expenditureForm?.get('code');
    codeControl?.markAsDirty();
    codeControl?.updateValueAndValidity();
    if (!codeControl?.valid) {
      this._notice.error(this._translateService.instant('settlement.ExpenseDetail.请检查单据编号'), '');
      return;
    }
    const payload = this.handleValue();
    this._service?.setSave(payload).subscribe((res) => {
      if (res.code == 200) {
        this._msg.success(this._translateService.instant('flss.success.暂存成功'));
        this.isEdit = false;
        this._id = res?.data?.id;
        this._router.navigate(['/settlement/payable-report/cost/expense-detail', this._id]);
        this.getDetail();
      }
    });
  }
  // 提交
  private onCommit() {
    const isValid = this._flValidatorService.formIsInvalid(this.expenditureForm);
    if (isValid) {
      this._notice.error('请填写必填项', '');
      return;
    }
    const payload = this.handleValue(true);
    this._service?.setSave(payload).subscribe((res) => {
      if (res.code == 200) {
        this._msg.success(this._translateService.instant('flss.success.submit'));
        this.isEdit = false;
        this._id = res?.data?.id;
        this._router.navigate(['/settlement/payable-report/cost/expense-detail', this._id]);
        this.getDetail();
      }
    });
  }
  // 处理提交数据
  private handleValue(is_commit = false) {
    const cloneData = this.expenditureForm.getRawValue();
    const occurred_at_start = cloneData?.occurred_at?.[0] ? getTime(cloneData?.occurred_at?.[0]) : null;
    const occurred_at_end = cloneData?.occurred_at?.[1] ? getTime(cloneData?.occurred_at?.[1]) : null;
    cloneData?.lines?.forEach((line: any) => {
      line.amount = String(line?.amount ?? 0);
    });
    delete cloneData.occurred_at;
    const payload = {
      ...cloneData,
      occurred_at_start,
      occurred_at_end,
      is_commit,
    };

    return payload;
  }

  // 退回修改 / 取消
  private onBackModify(type: 'modify' | 'undo') {
    this._modalService.create({
      nzContent: ReturnModelComponent,
      nzWidth: 400,
      nzClosable: false,
      nzWrapClassName: 'flc-confirm-modal',
      nzFooter: null,
      nzComponentParams: {
        title: type == 'modify' ? '退回修改' : '取消申请',
      },
      nzOnOk: (comp: any) => {
        const _value = comp.formGroup.getRawValue();
        const _service =
          type == 'modify'
            ? this._payableReportService.rejectExpenseReport({ ids: [Number(this._id)], reason: _value.reason })
            : this._service.setCancel({ id: Number(this._id), cancel_reason: _value.reason });
        _service.subscribe((result: any) => {
          if (result.data) {
            this._msg.success(type == 'modify' ? '退回成功' : '取消成功');
            this.getDetail();
          }
        });
      },
    });
  }
  // 审核通过
  private onApproved() {
    const ref = this._flcModal.confirmCancel(
      {
        content: this._translateService.instant('settlement.ExpenseDetail.approveTip'),
        subContent: this._translateService.instant('settlement.ExpenseDetail.approveTip2'),
        showSubContent: true,
        showIcon: false,
        strongConfirm: true,
        btnShape: 'round',
      },
      '334px'
    );
    ref.afterClose.subscribe((res) => {
      if (res) {
        this._payableReportService
          .approveExpenseReport({ ids: [Number(this._id)] })
          // ?.pipe(finalize(() => (this.loading = false)))
          ?.subscribe((res: any) => {
            if (res?.code === 200) {
              this._msg.success(this._translateService.instant('flss.success.审核通过成功'));
              this.getDetail();
            }
          });
      }
    });
  }
}
