<flss-payable-report-component-panel title="分摊明细" [titleRightAreaTpl]="titleRightAreaTpl">
  <form nz-form [formGroup]="allocationForm">
    <ng-container *ngIf="allocation_detail_list.controls.length">
      <nz-table [nzData]="[1]" [nzShowPagination]="false" [nzScroll]="{ x: '1200px' }" nzBordered>
        <thead>
          <tr>
            <th nzLeft nzWidth="46px">
              <a class="setting-btn" [ngStyle]="{ color: btnHighLight ? '#4D96FF' : '' }" (click)="changeHeader($event)">
                <i nz-icon nzType="setting" nzTheme="fill"></i>
              </a>
            </th>
            <ng-container *ngFor="let item of renderHeaders">
              <th
                [nzLeft]="item.disable || item.pinned ? item?.pinnedWidth ?? true : false"
                nz-resizable
                *ngIf="item.visible"
                nzPreview
                [nzWidth]="item.width"
                (nzResizeEnd)="onResize($event, item.label)">
                {{ item.label }}
                <nz-resize-handle nzDirection="right">
                  <div class="resize-trigger"></div>
                </nz-resize-handle>
              </th>
            </ng-container>
            <th nzRight nzWidth="90px" *ngIf="editMode !== pageEditModeEnum.read">
              {{ 'flss.common.action' | translate }}
            </th>
          </tr>
        </thead>
        <tbody formArrayName="allocation_detail_list">
          <tr *ngFor="let control of allocation_detail_list.controls; index as i" [formGroupName]="i">
            <td nzLeft>{{ i + 1 }}</td>
            <ng-container *ngFor="let col of renderHeaders">
              <td *ngIf="col.visible" [nzLeft]="col.disable || col.pinned ? col?.pinnedWidth ?? true : false">
                <!-- 分摊金额 -->
                <ng-container *ngIf="col.key === 'allocation_amount'">
                  <ng-container *ngIf="editMode === pageEditModeEnum.read">
                    {{ control.get(col.key)?.value || '-' }}
                  </ng-container>
                  <ng-container *ngIf="editMode !== pageEditModeEnum.read">
                    <nz-form-item>
                      <nz-form-control>
                        <nz-input-number
                          class="auto-width-input"
                          [nzMin]="0"
                          [nzPrecision]="4"
                          [nzStep]="0.0001"
                          [formControlName]="col.key">
                        </nz-input-number>
                      </nz-form-control>
                    </nz-form-item>
                  </ng-container>
                </ng-container>

                <!-- 分摊比例 -->
                <ng-container *ngIf="col.key === 'allocation_ratio'">
                  <ng-container *ngIf="editMode !== pageEditModeEnum.read; else ratioReadonlyTpl">
                    <nz-form-item>
                      <nz-form-control>
                        <nz-input-number-group [nzAddOnAfter]="'%'">  
                          <nz-input-number
                            [formControlName]="col.key"
                            [nzPlaceHolder]="'请输入'"
                            [nzPrecision]="2"
                            [nzMax]="100"
                            [nzMin]="0"
                            [nzStep]="0.01"
                            class="auto-width-input">
                          </nz-input-number>
                        </nz-input-number-group>
                      </nz-form-control>
                    </nz-form-item>
                  </ng-container>
                  <ng-template #ratioReadonlyTpl>
                    {{ control.get(col.key)?.value | number: '1.0-2' }}%
                  </ng-template>
                </ng-container>

                <!-- 入库日期 -->
                <ng-container *ngIf="col.key === 'inbound_time'">
                  {{ control.get(col.key)?.value | date: 'yyyy-MM-dd' || '-' }}
                </ng-container>

                <!-- 其他字段 -->
                <ng-container *ngIf="col.key !== 'allocation_amount' && col.key !== 'allocation_ratio' && col.key !== 'inbound_time'">
                  {{ control.get(col.key)?.value || '-' }}
                </ng-container>
              </td>
            </ng-container>
            <td *ngIf="editMode !== pageEditModeEnum.read" nzRight>
              <a
                nz-button
                nzType="link"
                flButton="link"
                class="error-color"
                (click)="onRemove(i)">
                <i nz-icon [nzIconfont]="'icon-caozuolan_shanchu1'"></i>
              </a>
            </td>
          </tr>
        </tbody>
        <!-- 合计行 -->
        <tbody *ngIf="allocation_detail_list.controls.length > 0">
          <tr class="total-row">
            <td [nzLeft]="true"><strong>合计</strong></td>
            <ng-container *ngFor="let col of renderHeaders; index as k">
              <td *ngIf="col.visible" [nzLeft]="col.disable || col.pinned ? col?.pinnedWidth ?? true : false">
                <!-- 入库数量合计 -->
                <ng-container *ngIf="col.key === 'inbound_quantity'">
                  <strong>{{ statisticData.total_inbound_quantity | number: '1.0-0' }}</strong>
                </ng-container>

                <!-- 分摊金额合计 -->
                <ng-container *ngIf="col.key === 'allocation_amount'">
                  <strong>{{ statisticData.total_amortization_amount | number: '1.0-2' }}</strong>
                </ng-container>

                <!-- 分摊比例合计 -->
                <ng-container *ngIf="col.key === 'allocation_ratio'">
                  <strong>{{ statisticData.total_amortization_rate | number: '1.0-2' }}%</strong>
                </ng-container>

                <!-- 其他字段显示为空 -->
                <ng-container *ngIf="col.key !== 'inbound_quantity' && col.key !== 'allocation_amount' && col.key !== 'allocation_ratio'">
                  -
                </ng-container>
              </td>
            </ng-container>
            <td *ngIf="editMode !== pageEditModeEnum.read" nzRight>
              <!-- 操作列为空 -->
            </td>
          </tr>
        </tbody>
      </nz-table>
    </ng-container>
    <div class="no-data-container" *ngIf="!allocation_detail_list.controls.length">
      <flc-no-data noDataText="暂无数据"></flc-no-data>
    </div>
  </form>
</flss-payable-report-component-panel>

<ng-template #titleRightAreaTpl>
  <button
    nz-button
    flButton="minor"
    nzShape="round"
    (click)="onAddDetail()"
    [disabled]="disabledBtn || totalAllocationAmount <= 0"
    *ngIf="editMode !== pageEditModeEnum.read"
    nz-tooltip
    [nzTooltipTitle]="totalAllocationAmount <= 0 ? '请先添加分摊费用' : ''">
    <i nz-icon [nzIconfont]="'icon-xinjian1'"></i>选择明细
  </button>
</ng-template>

<!-- 抽屉组件 -->
<ng-template #drawerTpl>
  <div style="margin-top: -8px; margin-bottom: 0">
    <flc-screen-container (reset)="resetList()">
      <div #searchBarWrap class="search-container">
        <div>
          入库日期：
          <nz-range-picker [(ngModel)]="searchData.inbound_time" [nzAllowClear]="true" (ngModelChange)="onSearch()"> </nz-range-picker>
        </div>
        <div>
          客户：
          <nz-select
            [(ngModel)]="searchData.customer_id"
            (ngModelChange)="onSearch()"
            [nzPlaceHolder]="'flss.placeholder.select' | translate"
            [nzBackdrop]="true"
            [nzShowSearch]="true"
            [nzAllowClear]="true"
            [nzOptions]="optionsList.customer || []">
          </nz-select>
        </div>
        <div>
          订单：
          <nz-select
            [(ngModel)]="searchData.order_uuid"
            (ngModelChange)="onSearch()"
            [nzPlaceHolder]="'flss.placeholder.select' | translate"
            [nzBackdrop]="true"
            [nzShowSearch]="true"
            [nzAllowClear]="true"
            [nzOptions]="optionsList.order || []">
          </nz-select>
        </div>
        <div>
          款号：
          <nz-select
            [(ngModel)]="searchData.style_uuid"
            (ngModelChange)="onSearch()"
            [nzPlaceHolder]="'flss.placeholder.select' | translate"
            [nzBackdrop]="true"
            [nzShowSearch]="true"
            [nzAllowClear]="true"
            [nzOptions]="optionsList.style || []">
          </nz-select>
        </div>
      </div>
    </flc-screen-container>

    <div class="center-container">
      <div class="selector-tooltip">
        <!-- 提示信息区域 -->
      </div>
      <div class="center-container-left">
        <div class="left-count">已选中{{ selectData.length }}数据</div>
        <button
          nz-button
          flButton="default-negative-danger"
          nzShape="round"
          [disabled]="!selectData.length || tableConfig.loading"
          nzDanger
          (click)="onClear()">
          {{ 'flss.btn.清空' | translate }}
        </button>
        <button nz-button flButton="minor" nzShape="round" [disabled]="!selectData.length || tableConfig.loading" (click)="handleOk()">
          {{ 'flss.btn.批量选择' | translate }}
        </button>
      </div>
    </div>

    <flc-table
      [tableHeader]="tableHeader"
      [tableConfig]="tableConfig"
      [template]="tableTemplate"
      (indexChanges)="indexChanges($event)"
      (sizeChanges)="sizeChanges($event)"
      (getCount)="getCount($event)">
    </flc-table>
    <ng-template let-data="data" #tableTemplate>
      <!-- 操作模板 -->
      <ng-container *ngIf="data.isAction">
        <a nz-button nzType="link" (click)="onSelect(data.item)" [disabled]="data?.item?.disabled">
          {{ 'flss.btn.选择' | translate }}
        </a>
      </ng-container>

      <!-- 订单号 -->
      <ng-container *ngIf="data.key === 'order_code'">
        <flc-text-truncated [data]="data.item.order_code || '-'"></flc-text-truncated>
      </ng-container>
    </ng-template>
  </div>
</ng-template>
