import { HttpClient } from '@angular/common/http';
import { NgModule } from '@angular/core';
import { TranslateLoader, TranslateModule, TranslateService } from '@ngx-translate/core';
import { TranslateHttpLoader } from '@ngx-translate/http-loader';
import { FlcOssUploadService } from 'fl-common-lib';
import { BasicArchiveRoutingModule } from './basic-archive-routing.module';
import { BasicArchiveService } from './basic-archive.service';
import { CurrencyDetailModalComponent } from './custom-record/components/currenncy-detail-modal/currenncy-detail-modal.component';
@NgModule({
  imports: [
    BasicArchiveRoutingModule,
    TranslateModule.forChild({
      isolate: false,
      extend: true,
      loader: {
        provide: TranslateLoader,
        useFactory: (http: HttpClient) => {
          return new TranslateHttpLoader(http, './assets/i18n/basic-archive/', '.json');
        },
        deps: [HttpClient],
      },
    }),
  ],
  exports: [],
  declarations: [],
  providers: [FlcOssUploadService, BasicArchiveService, CurrencyDetailModalComponent],
})
export class BasicArchiveModule {
  constructor(public translateService: TranslateService, public ossUploadService: FlcOssUploadService) {
    const cl = translateService.currentLang;
    translateService.currentLang = '';
    translateService.use(cl);

    // oss upload service模块级别配置信息
    ossUploadService.setOssNamespace('basic-archive');
    ossUploadService.setOssAuthUrl('/service/frontapi/v1/oss');
    ossUploadService.setOssAuthParams({
      biz_type: 'order',
    });
    const userInfo: string | null = localStorage.getItem('userInfo');
    if (userInfo) {
      const user = JSON.parse(userInfo);
      ossUploadService.setFilePath(`basic-archive/${user?.factory_code}`);
    }
  }
}
