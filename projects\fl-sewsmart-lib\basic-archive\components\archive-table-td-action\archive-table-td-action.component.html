<div class="table-action">
  <nz-switch
    *ngIf="config.status"
    [ngModel]="!!data.status"
    [nzCheckedChildren]="'archiveTableTdAction.启用' | translate"
    [nzUnCheckedChildren]="'archiveTableTdAction.禁用' | translate"
    [nzControl]="true"
    [nzDisabled]="!statusEnabled"
    (click)="handleStatus()"></nz-switch>
  <!-- 额度文本链接按钮 -->
  <ng-container *ngFor="let item of extraTextActions">
    <a style="margin-left: 8px" nz-button nzType="link" class="detail" (click)="handleAction(item)">
      {{ item.text }}
    </a>
  </ng-container>
  <a style="margin-left: 8px" *ngIf="config.detail" nz-button nzType="link" class="detail" (click)="handleDetail()">
    {{ 'flss.common.goDetail' | translate }}
  </a>
  <div class="separated" *ngIf="(config.status || config.detail || extraTextActions.length > 0) && modeList.length > 0"></div>
  <ng-container *ngFor="let item of modeList">
    <flss-archive-icon-popover
      [config]="item"
      [id]="data.id"
      [disabled]="data.is_system || data.create_type === 1"
      (action)="handleAction($event)"
      [popoverTextConfig]="popoverTextConfig"></flss-archive-icon-popover>
  </ng-container>
</div>
