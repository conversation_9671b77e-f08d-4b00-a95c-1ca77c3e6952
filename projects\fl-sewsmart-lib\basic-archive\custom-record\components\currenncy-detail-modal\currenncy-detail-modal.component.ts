import { Component, OnInit, Input } from '@angular/core';
import { FormBuilder } from '@angular/forms';
import { FlcValidatorService } from 'fl-common-lib';
import { NzModalRef } from 'ng-zorro-antd/modal';

import { NzNotificationService } from 'ng-zorro-antd/notification';

@Component({
  selector: 'flss-currency-detail-modal',
  templateUrl: './currenncy-detail-modal.component.html',
  styleUrls: ['./currenncy-detail-modal.component.scss'],
})
export class CurrencyDetailModalComponent implements OnInit {
  @Input() customerId: string | number = '';
  @Input() customerName = '';
  @Input() paymentQuotas: any[] = [];

  // 币种表格配置
  currencyTableHeader: any[] = [
    {
      label: '币种',
      key: 'currency_name',
      type: 'text',
      width: '200px',
      visible: true,
      disable: false,
      pinned: false,
      resizeble: true,
      sort: false,
    },
    {
      label: '额度',
      key: 'amount',
      type: 'text',
      width: '200px',
      visible: true,
      disable: false,
      pinned: false,
      resizeble: true,
      sort: false,
    },
  ];

  currencyTableConfig: any = {
    dataList: [],
    showPagination: false,
    hasAction: false,
    hasCheckbox: false,
    orderBy: [],
    count: 0,
    height: 400,
    loading: false,
    translateName: 'customRecordCommonFiled.',
    tableName: 'currency-detail-table',
    version: '1.0.0',
    canSetHeader: false, // 取消表头字段设置功能
  };

  constructor(
    private _fb: FormBuilder,
    private modelRef: NzModalRef,
    private _notifyService: NzNotificationService,
    private _validatorService: FlcValidatorService
  ) {}

  ngOnInit() {
    // 初始化表格数据
    this.initTableData();
  }

  // 初始化表格数据
  initTableData() {
    this.currencyTableConfig = {
      ...this.currencyTableConfig,
      dataList: [...this.paymentQuotas],
      count: this.paymentQuotas.length,
    };
  }

  onCloseModal() {
    this.modelRef.close();
  }

  onTriggerOk() {}
}
