import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { debounceTime, Subject, Subscription } from 'rxjs';
import { BasicArchiveService } from '../../basic-archive.service';

@Component({
  selector: 'flss-archive-table-td-action',
  templateUrl: './archive-table-td-action.component.html',
  styleUrls: ['./archive-table-td-action.component.scss'],
})
export class ArchiveTableTdActionComponent implements OnInit {
  @Input() uaType: 'basic-archive' | 'product-plan-archive' | 'supplier-management' | 'settings' | 'sys-settings' = 'basic-archive';
  @Input() uaKey!:
    | 'material-fabric'
    | 'material-accessory'
    | 'material-category'
    | 'category'
    | 'color'
    | 'size'
    | 'unit'
    | 'supplier'
    | 'part'
    | 'equipment'
    | 'factory'
    | 'band'
    | 'brand'
    | 'series'
    | 'style'
    | 'sample-style'
    | 'settlement'
    | 'custom-record'
    | 'card-template'
    | 'supplier-style'
    | 'plan-table'
    | 'party-a'
    | 'size-lib'
    | 'craft-lib'
    | 'risk-set'
    | 'settlement-adjustment'
    | 'account-management';
  @Input() data: any = {};
  @Input() uaTypePath = '';
  /**传递给popover 逆向操作需要的提示文案 */
  @Input() set popoverTextConfig(value: {
    type?: '颜色' | '尺码' | '款式' | '单位' | '供应商' | '部位' | '原料' | '机器' | '颜色组' | '尺码组' | '分科部位' | string;
    delete?: string;
    remove?: string;
  }) {
    this._popoverTextConfig = { ...this._popoverTextConfig, ...value };
  }
  get popoverTextConfig() {
    return this._popoverTextConfig;
  }
  _popoverTextConfig: any = {
    delete: this.getMessageValue('archiveTableTdAction.此操作不可撤回，数据将不会保存。'),
  };
  @Output() status: any = new EventEmitter();
  @Output() detail: any = new EventEmitter();
  @Output() action: any = new EventEmitter();
  @Input() set config(value: { status?: boolean; detail?: boolean }) {
    this._config = { ...this._config, ...value };
  }
  get config() {
    return this._config;
  }
  _config: any = {
    status: true,
    detail: true,
  };
  @Input() modeType: any = '';
  modeList?: any = [];
  statusEnabled = true;
  /******************* 组件内置 modelList start ********************/
  editConfig = [
    {
      icon: 'icon-caozuolan_bianji1',
      className: 'primary',
      key: 'edit',
      popoverTrigger: null,
    },
  ];
  deleteConfig = [
    {
      icon: 'icon-caozuolan_shanchu1',
      className: 'danger',
      key: 'delete',
      popoverTrigger: 'click',
    },
  ];

  default = [...this.editConfig, ...this.deleteConfig];
  // 款式分类
  category = [...this.editConfig];
  // 单位
  unit = [...this.editConfig, ...this.deleteConfig];
  // 供应商
  supplier = [...this.editConfig, ...this.deleteConfig];
  // 机器档案
  equipment = [...this.editConfig, ...this.deleteConfig];
  // 颜色
  color = [
    ...this.editConfig,
    ...this.deleteConfig,
    {
      icon: 'icon-yichu',
      className: 'danger',
      key: 'remove',
      popoverTrigger: 'click',
    },
  ];
  // 尺码
  size = [
    ...this.editConfig,
    ...this.deleteConfig,
    {
      icon: 'icon-yichu',
      className: 'danger',
      key: 'remove',
      popoverTrigger: 'click',
    },
  ];
  // 部位
  part = [
    ...this.editConfig,
    ...this.deleteConfig,
    {
      icon: 'icon-yichu',
      className: 'danger',
      key: 'remove',
      popoverTrigger: 'click',
    },
  ];
  // 原料档案
  material = [
    {
      icon: 'icon-caozuolan_fuzhi',
      className: 'primary',
      key: 'copy',
      popoverTrigger: null,
    },
    ...this.deleteConfig,
  ];
  // 加工厂
  factory = [...this.deleteConfig];
  // 品牌档案
  brand = [...this.editConfig, ...this.deleteConfig];
  // 企划波段
  band = [...this.editConfig, ...this.deleteConfig];
  // 系列档案
  series = [...this.editConfig, ...this.deleteConfig];
  // 风格档案
  style = [...this.editConfig, ...this.deleteConfig];
  // 样板类型
  'sample-style' = [...this.editConfig, ...this.deleteConfig];
  // 深铺任务流管理
  'deepflow-task-flow-management' = [...this.editConfig];
  // 供应商类型
  'supplier-style' = [...this.editConfig, ...this.deleteConfig];
  settlement = [...this.editConfig, ...this.deleteConfig];
  'custom-record' = [...this.deleteConfig];
  'card-template' = [...this.editConfig, ...this.deleteConfig];
  'progress-template' = [
    {
      icon: 'icon-caozuolan_fuzhi',
      className: 'primary',
      key: 'copy',
      popoverTrigger: null,
    },
    ...this.deleteConfig,
    ...this.editConfig,
  ];

  'plan-table' = [];

  'size-lib' = [];
  'craft-lib' = [...this.editConfig];
  'craft-type' = [...this.editConfig, ...this.deleteConfig];

  'risk-set' = [...this.editConfig];
  'risk-type' = [...this.editConfig, ...this.deleteConfig];

  'account-management' = [...this.editConfig, ...this.deleteConfig]; // 账户管理
  'none' = [];

  @Input() extraActionConfig: Array<any> = [];

  // 额度文本链接按钮（显示在详情前面）
  extraTextActions: Array<any> = [];

  subject$ = new Subject<void | boolean>();
  listSubscription?: Subscription;
  /******************* 组件内置 modelList end ********************/
  constructor(private _basicArchiveCommonService: BasicArchiveService, private _translate: TranslateService) {}

  ngOnInit() {
    this.initUserActions();

    // 分离文本链接按钮和图标按钮
    this.extraTextActions = this.extraActionConfig.filter((item) => item.text);
    const iconActions = this.extraActionConfig.filter((item) => !item.text);

    this.modeList = [...this[this.modeType || 'default'], ...iconActions];
    this.listSubscription = this.subject$.pipe(debounceTime(300)).subscribe(() => {
      this.status.emit({ status: !this.data.status, data: this.data });
    });
  }
  ngOnDestroy(): void {
    this.listSubscription?.unsubscribe();
  }
  initUserActions() {
    const actionMap = this._basicArchiveCommonService.getUserActionsMap();
    if (!actionMap) return;

    let key;
    if (['material-fabric', 'material-accessory', 'material-category'].includes(this.uaKey)) {
      key = this.uaKey.split('-')[0];
    } else {
      key = this.uaKey;
    }
    const actionList = this.uaTypePath ? actionMap.get(`${this.uaTypePath}/${key}`) : actionMap.get(`${this.uaType}/${key}`);
    if (!actionList) return;

    if (this.uaKey === 'category') {
      // 款式分类权限相关
      const _modeConfigs: Array<any> = [];
      actionList.includes(`${this.uaType}:category-update`) && _modeConfigs.push(...this.editConfig);
      this.category = _modeConfigs;
      if (!actionList.includes(`${this.uaType}:category-delete`)) {
        this.extraActionConfig = [];
      }
    } else {
      // 原料/颜色/尺码/单位/部位/机器/档案权限相关
      // 企划开发档案：品牌档案/企划波段/系列档案/风格档案权限相关
      // 供应商管理：供应商档案/加工厂档案相关权限
      const _key = this.uaKey;
      const _modeConfigs: Array<any> = [];
      const _defaultConfigs: Array<any> = [];
      const _can_copy = ['material-fabric', 'material-accessory'].includes(_key)
        ? actionList.includes(`${this.uaType}:${_key}-create`)
        : false;
      const _can_edit = ['material-fabric', 'material-accessory', 'factory', 'custom-record', 'size-lib'].includes(_key)
        ? false
        : actionList.includes(`${this.uaType}:${_key}-update`);
      // 供应商类型的删除权限是supplier-style-update
      const _can_delete =
        _key === 'supplier-style'
          ? actionList.includes(`${this.uaType}:${_key}-update`)
          : actionList.includes(`${this.uaType}:${_key}-delete`);
      const _can_bind = ['color', 'size', 'part'].includes(_key) ? actionList.includes(`${this.uaType}:${_key}-bind`) : false;
      _can_copy &&
        _modeConfigs.push({
          icon: 'icon-caozuolan_fuzhi',
          className: 'primary',
          key: 'copy',
          popoverTrigger: null,
        });
      _can_edit && _modeConfigs.push(...this.editConfig) && _defaultConfigs.push(...this.editConfig);
      _can_delete && _modeConfigs.push(...this.deleteConfig) && _defaultConfigs.push(...this.deleteConfig);
      _can_bind &&
        _modeConfigs.push({
          icon: 'icon-yichu',
          className: 'danger',
          key: 'remove',
          popoverTrigger: 'click',
        });

      if (['', 'default'].includes(this.modeType)) {
        this.default = _defaultConfigs;
      } else {
        this[['material-fabric', 'material-accessory'].includes(this.uaKey) ? 'material' : this.uaKey] = _modeConfigs;
      }
    }

    // 启禁用权限相关
    const _key = this.uaKey;
    this.statusEnabled = actionList.includes(`${this.uaType}:${_key}-update`);
  }
  handleAction(modelItem: any) {
    this.action.emit({ key: modelItem.key, data: this.data, needConfirm: modelItem?.needConfirm, tipLevel: modelItem?.tipLevel });
  }

  handleStatus() {
    if (this.statusEnabled) {
      this.removePopover();
      this.subject$.next();
    }
  }
  handleDetail() {
    this.removePopover();
    this.detail.emit({ key: 'detail', data: this.data });
  }
  getMessageValue(key: string): string {
    return this._translate.instant(key);
  }
  // 删除popover 元素
  removePopover() {
    const element = document.querySelector('.archive-td-action-popover-panel');
    if (element) {
      element.remove();
    }
  }
}
