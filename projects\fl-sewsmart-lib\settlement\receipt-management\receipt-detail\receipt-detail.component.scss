.btn-box {
  button {
    margin-left: 8px;
  }
}
.head-title {
  display: flex;
  align-items: center;

  .header-tip {
    background: #e7f3fe;
    border-radius: 14px;
    font-size: 14px;
    font-weight: 500;
    color: #138aff;
    line-height: 20px;
    padding: 2px 6px;

    &.status-2 {
      background: #feefe5;
      color: #fb6401;
    }

    &.status-3 {
      background: #e4ebef;
      color: #54607c;
    }

    &.status-4 {
      background: #e6e6e6;
      color: #97999c;
    }
  }
}
.receipt-detail-container {
  // padding: 24px;
  // background: #f5f5f5;
  // min-height: 100vh;

  .receipt-form {
    // max-width: 1200px;
    margin: 0 auto;
  }

  .reason-container {
    display: flex;
    align-items: center;
    background-color: #fee4e4;
    padding: 0 12px 4px 12px;
    border-radius: 12px 12px 0px 0px;
    color: #f74949;

    & > span {
      flex-basis: 100px;
      margin-left: 4px;
      font-size: 16px;
      flex-shrink: 0;
    }
  }

  .form-section {
    background: #fff;
    border-radius: 8px;
    // margin-bottom: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    &::before {
      position: relative;
      z-index: 9;
      display: block;
      width: 100%;
      content: '';
      height: 16px;
      border-top: 1px dashed #d4d7dc;
    }
    .section-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 24px;
      border-bottom: 1px solid #f0f0f0;
      font-size: 16px;
      font-weight: 500;
      color: #262626;
      background: #fafafa;
      border-radius: 8px 8px 0 0;
    }

    .form-content {
      padding: 24px;
    }

    .table-container {
      padding: 0;

      .summary-row {
        color: #158aff;
      }
    }
  }

  .form-actions {
    display: flex;
    justify-content: center;
    gap: 16px;
    padding: 24px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
}

// 全局样式覆盖
:host ::ng-deep {
  .ant-form-item {
    margin-bottom: 24px;
  }

  .ant-form-item-label {
    padding-bottom: 8px;
  }

  .ant-form-item-label > label {
    font-size: 14px;
    color: #262626;
    font-weight: 500;
  }

  .ant-form-item-label > label.ant-form-item-required::before {
    color: #ff4d4f;
  }

  .ant-input,
  .ant-select-selector,
  .ant-picker,
  .ant-input-number {
    border-radius: 6px;
    border: 1px solid #d9d9d9;

    &:hover {
      border-color: #40a9ff;
    }

    &:focus,
    &.ant-input-focused,
    &.ant-select-focused .ant-select-selector,
    &.ant-picker-focused {
      border-color: #40a9ff;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    }
  }

  // 表格样式
  .flc-table {
    .ant-table {
      border-radius: 0;
    }

    .ant-table-thead > tr > th {
      background: #fafafa;
      font-weight: 500;
      color: #262626;
      border-bottom: 1px solid #f0f0f0;
      padding: 12px 16px;
    }

    .ant-table-tbody > tr > td {
      padding: 12px 16px;
      border-bottom: 1px solid #f0f0f0;
    }

    .ant-table-tbody > tr:hover > td {
      background: #f5f5f5;
    }

    .ant-table-tbody > tr:last-child > td {
      border-bottom: none;
    }
  }

  // 输入框样式
  .ant-input-number {
    width: 100%;

    &.ant-input-number-sm {
      .ant-input-number-input {
        height: 28px;
        padding: 0 8px;
      }
    }
  }

  // 选择器样式
  .ant-select {
    width: 100%;
  }

  // 日期选择器样式
  .ant-picker {
    width: 100%;
  }

  // 文本域样式
  .ant-input {
    &[nz-input] {
      resize: none;
    }
  }

  // 加载状态
  .ant-spin-container {
    min-height: 200px;
  }

  // 预收款显示样式
  .advance-amount-display {
    // display: flex;
    // flex-direction: column;
    // align-items: flex-start;
    // line-height: 1.4;

    .used-amount {
      color: #262626;
    }

    .remaining-amount {
      color: #8c8c8c;
    }
  }

  // 预收款输入组合样式
  .advance-input-group {
    // display: flex;
    // align-items: center;
    // gap: 8px;

    .ant-input-number {
      flex: 1;
      min-width: 100px;
    }

    .ant-checkbox-wrapper {
      white-space: nowrap;
      font-size: 12px;
    }
  }

  // 表格汇总行样式
  .summary-row {
    background-color: #f8f9fa !important;
    font-weight: 500;

    td {
      border-top: 2px solid #e9ecef !important;
      color: #495057;
    }
  }

  // 删除按钮样式
  .delete-hover {
    // color: #ff4d4f;

    &:hover {
      color: #ff7875;
    }
  }

  // 合并单元格样式
  .merged-cell {
    border-top: none !important;
    position: relative;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 1px;
      background: transparent;
    }
  }

  .merge-border {
    border-top: 1px solid #f0f0f0 !important;
  }

  // 表格行合并样式
  .ant-table-tbody > tr > td {
    &.merged-cell {
      border-top: none;
    }

    &.merge-border {
      border-top: 1px solid #f0f0f0;
    }
  }
}
