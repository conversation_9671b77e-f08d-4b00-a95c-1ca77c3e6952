import { Component, OnInit, ViewChild, AfterViewInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Location } from '@angular/common';
import { PayableReportService } from '../../payable-report.service';
import { NzMessageService } from 'ng-zorro-antd/message';
import { NzModalService } from 'ng-zorro-antd/modal';
import { PayableReportRefuseComponent } from '../components/refuse-modal/refuse-modal.component';
import { PageEditModeEnum, OperateButtonEnum } from '../../models/cost-type.interface';
import { BroadcastDefualtKey, BroadcastService } from 'fl-common-lib';
import { SaveExpenseAllocationRequest } from '../../models/payable-report-detail.interface';
@Component({
  selector: 'flss-allocation-detail',
  templateUrl: './allocation-detail.component.html',
  styleUrls: ['./allocation-detail.component.scss'],
  providers: [PayableReportService],
})
export class AllocationDetailComponent implements OnInit, AfterViewInit {
  editMode: PageEditModeEnum = PageEditModeEnum.add;
  detailInfo?: any;
  currentId?: string; // 当前分摊单ID

  // 分摊金额总数
  totalAllocationAmount: number = 0;

  // 缓存分摊金额数据，用于在ViewChild初始化后传递给子组件
  private cachedAmountData?: {
    totalAmount: number;
    categoryOneAmount: number;
    categoryTwoList: Array<{
      customer_id: number;
      order_uuid: string;
      style_code: string;
      amortization_amount: number;
    }>;
  };

  // ViewChild 引用子组件
  @ViewChild('baseinfoComponent') baseinfoComponent: any;
  @ViewChild('allocatedAmountComponent') allocatedAmountComponent: any;
  @ViewChild('allocationDetailComponent') allocationDetailComponent: any;
  constructor(
    private route: ActivatedRoute,
    private _router: Router,
    private location: Location,
    private _service: PayableReportService,
    private messageService: NzMessageService,
    private _broadcast: BroadcastService,
    private _modalService: NzModalService
  ) {}

  ngOnInit(): void {
    // 检查路由参数，如果有 id 则加载详情
    this.route.params.subscribe((params) => {
      const id = params['id'];
      if (id && id !== 'add') {
        this.currentId = id; // 保存当前ID
        this.editMode = PageEditModeEnum.read;
        this.loadDetailInfo(Number(id));
        this._broadcast.yell({
          channel: BroadcastDefualtKey.changeTabTitle,
          message: {
            targetUrl: `${window.location.pathname}`,
            title: '费用分摊 - 详情',
          },
        });
      } else {
        this._broadcast.yell({
          channel: BroadcastDefualtKey.changeTabTitle,
          message: {
            targetUrl: `${window.location.pathname}`,
            title: '费用分摊 - 新建',
          },
        });
        this.editMode = PageEditModeEnum.add;
      }
    });
  }

  ngAfterViewInit(): void {
    // 如果有缓存的分摊金额数据，在ViewChild初始化后传递给子组件
    if (this.cachedAmountData && this.allocationDetailComponent) {
      this.allocationDetailComponent.setAllocationAmountData(this.cachedAmountData);
    }
  }

  onAction(action: OperateButtonEnum) {
    switch (action) {
      case OperateButtonEnum.back:
        this.onBack();
        break;
      case OperateButtonEnum.edit:
        this.editMode = PageEditModeEnum.edit;
        break;
      case OperateButtonEnum.save:
        this.onSave();
        break;
      case OperateButtonEnum.commit:
        this.onCommit();
        break;
      case OperateButtonEnum.cancel:
        this.onCancel();
        break;
      case OperateButtonEnum.cancelSettlement:
        this.onCancelSettlement();
        break;
      case OperateButtonEnum.pass:
        this.onPass();
        break;
      case OperateButtonEnum.modify:
        this.onModify();
        break;
      case OperateButtonEnum.export:
        // this.onExport();
        break;
      case OperateButtonEnum.unBindInvoice:
        // this.unBindInvoice();
        break;
    }
  }

  private onBack() {
    this._router.navigate(['/settlement/payable-report/cost'], { relativeTo: this.route });
  }

  /**
   * 保存
   */
  private onSave() {
    const saveData = this.collectAllData();
    if (saveData) {
      // 如果有currentId，添加到保存数据中
      if (this.currentId) {
        saveData.id = this.currentId;
      }

      this._service.saveExpenseAllocation(saveData).subscribe({
        next: (res) => {
          if (res.code === 200) {
            this.messageService.success('保存成功');

            // 获取返回的 id
            const savedId = res.data?.id;
            if (savedId) {
              this.currentId = savedId; // 更新currentId
            }
            if (PageEditModeEnum.add === this.editMode) {
              // 重新跳转到当前页面，传递 id 参数
              this._router.navigate(['/settlement/payable-report/cost/allocation-detail', savedId], {
                relativeTo: this.route,
              });
            }
            // 切换到只读模式
            this.editMode = PageEditModeEnum.read;

            // 更新页面标题
            this._broadcast.yell({
              channel: BroadcastDefualtKey.changeTabTitle,
              message: {
                targetUrl: `${window.location.pathname}`,
                title: '费用分摊 - 详情',
              },
            });

            // 重新获取最新数据
            if (this.currentId) {
              this.loadDetailInfo(Number(this.currentId));
            }
          } else {
            this.messageService.error(res.message || '保存失败');
          }
        },
        error: (err) => {
          this.messageService.error('保存失败');
          console.error('保存错误:', err);
        },
      });
    }
  }

  /**
   * 提交
   */
  private onCommit() {
    const submitData = this.collectAllData();
    if (submitData) {
      // 如果有currentId，添加到提交数据中
      if (this.currentId) {
        submitData.id = this.currentId;
      }

      this._service.submitExpenseAllocation(submitData).subscribe({
        next: (res) => {
          if (res.code === 200) {
            this.messageService.success('提交成功');
            // 获取返回的 id
            const savedId = res.data?.id;
            if (savedId) {
              this.currentId = savedId; // 更新currentId
            }
            if (PageEditModeEnum.add === this.editMode) {
              // 重新跳转到当前页面，传递 id 参数
              this._router.navigate(['/settlement/payable-report/cost/allocation-detail', savedId], {
                relativeTo: this.route,
              });
            }
            // 切换到只读模式
            this.editMode = PageEditModeEnum.read;

            // 更新页面标题
            this._broadcast.yell({
              channel: BroadcastDefualtKey.changeTabTitle,
              message: {
                targetUrl: `${window.location.pathname}`,
                title: '费用分摊 - 详情',
              },
            });

            // 重新获取最新数据
            if (this.currentId) {
              this.loadDetailInfo(Number(this.currentId));
            }
          } else {
            this.messageService.error(res.message || '提交失败');
          }
        },
        error: (err) => {
          this.messageService.error('提交失败');
          console.error('提交错误:', err);
        },
      });
    }
  }

  /**
   * 收集所有组件的数据并拼装成接口所需参数
   */
  private collectAllData(): SaveExpenseAllocationRequest | null {
    try {
      // 1. 获取基本信息组件数据
      const baseinfoData = this.getBaseinfoData();
      if (!baseinfoData) {
        this.messageService.error('请完善基本信息');
        return null;
      }

      // 2. 获取分摊金额组件数据（费用明细）
      const expenseLines = this.getExpenseLines();
      if (!expenseLines || expenseLines.length === 0) {
        this.messageService.error('请选择分摊费用');
        return null;
      }

      // 3. 获取分摊明细组件数据（分摊明细）
      const allocationLines = this.getAllocationLines();
      if (!allocationLines || allocationLines.length === 0) {
        this.messageService.error('请添加分摊明细');
        return null;
      }

      // 4. 拼装完整的保存参数
      const saveData: SaveExpenseAllocationRequest = {
        id: this.currentId || this.detailInfo?.id?.toString() || '0', // 优先使用currentId，新建时传入0
        code: baseinfoData.code,
        amortization_basis_type: baseinfoData.amortization_basis_type,
        amortization_mode_type: baseinfoData.amortization_mode_type,
        expense_lines: expenseLines,
        allocation_lines: allocationLines,
      };

      return saveData;
    } catch (error) {
      console.error('收集数据时发生错误:', error);
      this.messageService.error('数据收集失败，请检查表单');
      return null;
    }
  }

  /**
   * 获取基本信息组件数据
   */
  private getBaseinfoData(): any {
    if (!this.baseinfoComponent) {
      console.error('基本信息组件引用不存在');
      return null;
    }

    // 尝试获取表单数据 - 基本信息组件使用的是 baseInfoForm
    if (this.baseinfoComponent.baseInfoForm) {
      const formValue = this.baseinfoComponent.baseInfoForm.value;
      console.log('基本信息表单数据:', formValue);

      // 检查必填字段是否已填写
      if (!formValue.code) {
        console.error('分摊单号未填写');
        return null;
      }
      if (!formValue.basis_type) {
        console.error('业务单据未选择');
        return null;
      }
      if (!formValue.mode_type) {
        console.error('分摊方式未选择');
        return null;
      }

      // 返回符合保存接口格式的数据
      return {
        code: formValue.code,
        amortization_basis_type: formValue.mode_type, // 分摊方式
        amortization_mode_type: formValue.basis_type, // 业务单据
      };
    }

    console.error('无法获取基本信息组件数据 - baseInfoForm 不存在');
    return null;
  }

  /**
   * 获取分摊金额组件数据（费用明细）
   */
  private getExpenseLines(): any[] {
    if (!this.allocatedAmountComponent) {
      console.error('分摊金额组件引用不存在');
      return [];
    }

    // 使用我们之前添加的 getExpenseLines 方法
    if (typeof this.allocatedAmountComponent.getExpenseLines === 'function') {
      return this.allocatedAmountComponent.getExpenseLines();
    }

    console.error('无法获取分摊金额组件数据');
    return [];
  }

  /**
   * 获取分摊明细组件数据（分摊明细）
   */
  private getAllocationLines(): any[] {
    if (!this.allocationDetailComponent) {
      console.error('分摊明细组件引用不存在');
      return [];
    }

    // 假设分摊明细组件有 getAllocationLines 方法
    if (typeof this.allocationDetailComponent.getAllocationLines === 'function') {
      return this.allocationDetailComponent.getAllocationLines();
    }

    // 如果没有专门的方法，尝试获取表单数组数据
    if (this.allocationDetailComponent.allocation_detail_list) {
      return this.allocationDetailComponent.allocation_detail_list.controls.map((control: any) => control.value);
    }

    console.error('无法获取分摊明细组件数据');
    return [];
  }

  /**
   * 处理分摊金额总数变化
   */
  onTotalAmountChange(amountData: {
    totalAmount: number;
    categoryOneAmount: number;
    categoryTwoList: Array<{
      customer_id: number;
      order_uuid: string;
      style_code: string;
      amortization_amount: number;
    }>;
  }) {
    this.totalAllocationAmount = amountData.totalAmount;

    // 缓存分摊金额数据
    this.cachedAmountData = amountData;

    // 如果分摊明细组件已存在，立即更新其分摊金额数据
    if (this.allocationDetailComponent && typeof this.allocationDetailComponent.setAllocationAmountData === 'function') {
      this.allocationDetailComponent.setAllocationAmountData(amountData);
    } else {
    }
    // 如果组件还未初始化，数据会在ngAfterViewInit中传递
  }

  private onCancel() {
    if (this.editMode === PageEditModeEnum.edit) {
      this.editMode = PageEditModeEnum.read;
    } else {
      this.onBack();
    }
  }

  /**
   * 加载详情数据
   */
  private loadDetailInfo(id: number) {
    const request = { id: id.toString() };
    this._service.getExpenseAllocationDetail(request).subscribe({
      next: (res) => {
        if (res.code === 200 && res.data) {
          this.detailInfo = res.data;
          // console.log('获取详情成功:', res.data);
        } else {
          this.messageService.error(res.message || '获取详情失败');
        }
      },
      error: (err) => {
        this.messageService.error('获取详情失败');
        console.error('获取详情错误:', err);
      },
    });
  }

  private onCancelSettlement() {}

  /**
   * 审核通过
   */
  private onPass() {
    if (!this.currentId) {
      this.messageService.error('缺少分摊单ID');
      return;
    }

    // 直接发送审核通过请求，不需要二次确认
    const params = {
      ids: [Number(this.currentId!)], // 转换为number类型，符合接口定义
    };

    this._service.approveAllocationReport(params).subscribe({
      next: (res: any) => {
        if (res && (res.code === 200 || res.success !== false)) {
          this.messageService.success('审核通过成功');
          // 重新加载详情数据
          this.loadDetailInfo(Number(this.currentId));
        } else {
          this.messageService.error((res && res.message) || '审核通过失败');
        }
      },
      error: (error) => {
        this.messageService.error('审核通过失败');
        console.error('审核通过失败:', error);
      },
    });
  }

  /**
   * 审核驳回
   */
  private onModify() {
    if (!this.currentId) {
      this.messageService.error('缺少分摊单ID');
      return;
    }

    // 使用 refuse-modal 组件
    const modalRef = this._modalService.create({
      nzContent: PayableReportRefuseComponent,
      nzFooter: null,
      nzWidth: 520,
      nzMaskClosable: false,
      nzComponentParams: {
        title: '退回修改',
        placeholder: '请输入退回修改原因',
      },
    });

    modalRef.afterClose.subscribe((result) => {
      if (result && result.success) {
        const params = {
          ids: [Number(this.currentId!)], // 转换为number类型，符合接口定义
          reason: result.reason,
        };

        this._service.rejectAllocationReport(params).subscribe({
          next: (res: any) => {
            if (res && (res.code === 200 || res.success !== false)) {
              this.messageService.success('退回修改成功');
              // 重新加载详情数据
              this.loadDetailInfo(Number(this.currentId));
            } else {
              this.messageService.error((res && res.message) || '退回修改失败');
            }
          },
          error: (error) => {
            this.messageService.error('退回修改失败');
            console.error('退回修改失败:', error);
          },
        });
      }
    });
  }
}
