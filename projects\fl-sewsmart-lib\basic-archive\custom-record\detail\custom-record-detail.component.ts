import { Component, OnInit, ViewChild } from '@angular/core';
import { FormBuilder, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';

import { FlcTableComponent, FlcValidatorService, resizable, BroadcastService } from 'fl-common-lib';
import { NzMessageService } from 'ng-zorro-antd/message';

import { BtnConfig, DetailFormList, FormModeEnum } from '../custom-record.config';
import { CustomRecordService } from '../custom-record.service';

@Component({
  selector: 'flss-custom-record-detail',
  templateUrl: './custom-record-detail.component.html',
  styleUrls: ['./custom-record-detail.component.scss'],
  providers: [],
})
@resizable()
export class CustomRecordDetailComponent implements OnInit {
  @ViewChild('equipmentList') equipmentListRef!: FlcTableComponent;
  translateName = 'customRecordCommonFiled.';
  lang = localStorage.getItem('lang') || 'zh';

  btnConfig = BtnConfig;
  canEdit = false;
  categoryFormConfig = DetailFormList;
  id = null;
  detailData: any = {};
  currencyList: any[] = []; // 币种列表
  paymentQuotas: any[] = []; // 币种额度列表
  currencyLoading = false; // 币种列表加载状态
  currencyError = false; // 币种列表加载错误状态

  // 币种表格配置
  currencyTableHeader: any[] = [
    {
      label: '币种',
      key: 'currency_name',
      type: 'text',
      width: '200px',
      visible: true,
      disable: false,
      pinned: false,
      mode: [1, 2],
      resizeble: true,
      sort: false,
    },
    {
      label: '额度',
      key: 'amount',
      type: 'template',
      templateName: 'amount',
      width: '200px',
      visible: true,
      disable: false,
      pinned: false,
      mode: [1, 2],
      resizeble: true,
      sort: false,
    },
  ];

  currencyTableConfig: any = {
    dataList: [],
    showPagination: false,
    hasAction: false,
    hasCheckbox: false,
    orderBy: [],
    count: 0,
    height: 400,
    loading: false,
    translateName: 'customRecordCommonFiled.',
    tableName: 'currency-quota-table',
    version: '1.0.0',
    canSetHeader: false,
  };

  searchOptionFetchUrl = this._service.serviceUrl + 'customer/operation';

  paramsForm = this._fb.group({
    code: [null, Validators.required],
    name: [null, Validators.required],
    short_name: [null, Validators.required],
    status: [true, Validators.required],
    usci: [null],
    unit_id: [null],
    rate: [null],
    shipping_method: [null],
    receiver_country: [null],
    receiver_port: [null],
    address: [null],
    contact_name: [null],
    contact_phone: [null],
    deposit_bank: [null],
    bank_account: [null],
    gen_user_name: [null],
    remark: [null],
    related_user_ids: [null],
    related_user_names: [null],
    payment_method_id: [null],
    payment_method: [null],
    settlement_method_id: [null],
    settlement_method: [null],
    unit_name: [null],
    payment_quota: [null],
  });
  permissionMap: any = {};

  constructor(
    private _service: CustomRecordService,
    private _activeRoute: ActivatedRoute,
    private _router: Router,
    private _fb: FormBuilder,
    private _validator: FlcValidatorService,
    private _broadcast: BroadcastService,
    private message: NzMessageService
  ) {}

  ngOnInit() {
    // 权限
    const permissions = this._service.initUserActions();
    if (permissions) {
      this.permissionMap = {
        edit: permissions.includes(this._service.permissionKey + 'update'),
      };
    }

    this.id = this._activeRoute.snapshot.params?.id === 'new' ? null : this._activeRoute.snapshot.params?.id;
    if (this.id) {
      this._service.formMode = FormModeEnum.ReadOnly;
      this.getDetail();
    } else {
      this._service.formMode = FormModeEnum.Create;
    }

    this.getOptions();
    this.getCurrencyList();

    // 初始化表格数据
    this.updateCurrencyTableData();
  }
  get receiver_country() {
    let text = '';
    if (this.detailData?.receiver_country_name) {
      text = `${this.detailData?.receiver_country_name}`;
    }
    if (this.detailData?.province) {
      text = text + `-${this.detailData?.province}`;
    }
    if (this.detailData?.city) {
      text = text + `-${this.detailData?.city}`;
    }
    if (this.detailData?.district) {
      text = text + `-${this.detailData?.district}`;
    }

    return text;
  }
  get btnList() {
    let btn = ['cancel', 'save'];
    switch (this._service.formMode) {
      case FormModeEnum.Create:
        btn = ['back', 'save'];
        break;
      case FormModeEnum.ReadOnly:
        btn = this.permissionMap?.edit ? ['back', 'edit'] : ['back'];
        break;
      case FormModeEnum.Edit:
        btn = ['cancel', 'save'];
        break;
    }
    return btn;
  }
  getDetail() {
    this._service.getCustomerDetail({ id: this.id }).subscribe((res) => {
      if (res) {
        let receiver_country: number[] | null = [];
        if (!res.data.receiver_country_id) {
          receiver_country = null;
        } else if (!res.data?.province_id) {
          receiver_country = [res.data?.receiver_country_id];
        } else if (!res.data?.city_id) {
          receiver_country = [res.data?.receiver_country_id, res.data?.province_id];
        } else if (!res.data?.district_id) {
          receiver_country = [res.data?.receiver_country_id, res.data?.province_id, res.data?.city_id];
        } else {
          receiver_country = [
            res.data?.receiver_country_id ?? null,
            res.data?.province_id ?? null,
            res.data?.city_id ?? null,
            res.data?.district_id ?? null,
          ];
        }

        // 系统账号处理
        const related_user_ids: number[] = [];
        const related_user_names: string[] = [];

        this.defaultSysAccountValue = res.data.related_user?.map((d: any) => {
          related_user_ids.push(d.id);
          related_user_names.push(d.name);

          return {
            label: d.name,
            value: d.id,
          };
        });

        res.data.related_user_names = related_user_names.join('、');

        this.paramsForm.setValue({
          code: res.data.code,
          name: res.data.name,
          short_name: res.data.short_name,
          status: res.data.status === 1 ? true : false,
          usci: res.data.usci,
          unit_id: res.data.unit_id || null,
          unit_name: res.data.unit_name || null,
          payment_method_id: res.data.payment_method_id ? String(res.data.payment_method_id) : null,
          payment_method: res.data.payment_method || null,
          settlement_method_id: res.data.settlement_method_id ? String(res.data.settlement_method_id) : null,
          settlement_method: res.data.settlement_method || null,
          rate: res.data.rate,
          payment_quota: res.data.payment_quota,
          shipping_method: res.data.shipping_method,
          receiver_country,
          receiver_port: res.data.receiver_port,
          address: res.data.address,
          contact_name: res.data.contact_name,
          contact_phone: res.data.contact_phone,
          deposit_bank: res.data.deposit_bank,
          bank_account: res.data.bank_account,
          gen_user_name: res.data.gen_user_name,
          remark: res.data.remark,
          related_user_ids,
          related_user_names,
        });
        this.detailData = res.data;

        // 处理币种额度数据
        this.processPaymentQuotas(res.data.payment_quotas);
      }
    });
  }

  // 处理币种额度数据
  processPaymentQuotas(paymentQuotas: any[]) {
    if (paymentQuotas && Array.isArray(paymentQuotas)) {
      this.paymentQuotas = paymentQuotas.map((quota: any) => {
        // 从币种列表中找到对应的币种名称
        const currency = this.currencyList.find((c: any) => c.id === quota.currency_id);
        return {
          id: quota.id || 0, // 内部保持数字类型，便于查找和比较
          currency_id: quota.currency_id, // 内部保持原有类型
          currency_name: currency ? currency.name : quota.currency_name || '未知币种',
          amount: quota.amount || null,
        };
      });
    } else {
      this.paymentQuotas = [];
    }
    // 更新表格数据
    this.updateCurrencyTableData();
  }

  // 获取币种列表
  getCurrencyList() {
    // 新建模式：获取币种列表并初始化
    if (!this.id) {
      this.currencyLoading = true;
      this.currencyError = false;
      this.currencyTableConfig.loading = true;

      this._service.getCurrencyList().subscribe({
        next: (res) => {
          this.currencyLoading = false;
          this.currencyTableConfig.loading = false;
          if (res && res.data && res.data.datalist) {
            this.currencyList = res.data.datalist;
            // 初始化币种额度列表
            this.paymentQuotas = this.currencyList.map((currency: any) => ({
              id: 0, // 新建时为0
              currency_id: currency.id,
              currency_name: currency.name,
              amount: null,
            }));
            this.updateCurrencyTableData();
          } else {
            this.currencyError = true;
            this.paymentQuotas = [];
            this.updateCurrencyTableData();
          }
        },
        error: () => {
          this.currencyLoading = false;
          this.currencyTableConfig.loading = false;
          this.currencyError = true;
          this.paymentQuotas = [];
          this.updateCurrencyTableData();
        },
      });
    } else {
      // 详情模式：也需要获取币种列表以便显示币种名称
      this._service.getCurrencyList().subscribe({
        next: (res) => {
          if (res && res.data && res.data.list) {
            this.currencyList = res.data.list;
          }
        },
        error: () => {
          console.warn('获取币种列表失败');
        },
      });
    }
  }

  // 编辑模式下获取币种列表
  getCurrencyListForEdit() {
    this.currencyLoading = true;
    this.currencyError = false;
    this.currencyTableConfig.loading = true;

    this._service.getCurrencyList().subscribe({
      next: (res) => {
        this.currencyLoading = false;
        this.currencyTableConfig.loading = false;
        if (res && res.data && res.data.datalist) {
          this.currencyList = res.data.datalist;
          // 合并现有的payment_quotas数据和完整的币种列表
          this.mergePaymentQuotasWithCurrencyList();
        } else {
          this.currencyError = true;
        }
      },
      error: () => {
        this.currencyLoading = false;
        this.currencyTableConfig.loading = false;
        this.currencyError = true;
      },
    });
  }

  // 合并币种额度数据和币种列表
  mergePaymentQuotasWithCurrencyList() {
    const existingQuotas = [...this.paymentQuotas];

    // 基于完整的币种列表创建新的paymentQuotas
    this.paymentQuotas = this.currencyList.map((currency: any) => {
      // 查找是否已有该币种的额度数据
      const existingQuota = existingQuotas.find((quota) => quota.currency_id === currency.id);
      return {
        id: existingQuota ? existingQuota.id : 0, // 新建时为0
        currency_id: currency.id,
        currency_name: currency.name, // 确保使用正确的字段名
        amount: existingQuota ? existingQuota.amount : null,
      };
    });

    this.updateCurrencyTableData();
  }

  // 更新币种表格数据
  updateCurrencyTableData() {
    this.currencyTableConfig = {
      ...this.currencyTableConfig,
      dataList: [...this.paymentQuotas],
      count: this.paymentQuotas.length,
    };
  }

  get isDetail() {
    return this._service.formMode === FormModeEnum.ReadOnly ? true : false;
  }

  get title() {
    let title = '';
    switch (this._service.formMode) {
      case FormModeEnum.ReadOnly:
        title = '详情';
        break;
      case FormModeEnum.Create:
        title = '创建';
        break;
      case FormModeEnum.Edit:
        title = '编辑';
        break;
    }
    return title;
  }

  getOptions() {
    this._service.customerListOptions().subscribe((res) => {
      if (res) {
        this.categoryFormConfig.forEach((item: any) => {
          if (item.type === 'local-select') {
            item.options = res.data[item.columnkey];
          }
        });

        if (this.id) return;
        const options = this.categoryFormConfig.find((item: any) => item.code === 'unit_id')?.options || [];
        const _item: any = options.find((item: any) => item.value === '人民币');
        if (_item) {
          this.paramsForm.get('unit_id')?.setValue(_item.value, { emitViewToModelChange: false });
          this.paramsForm.get('unit_name')?.setValue(_item.label, { emitViewToModelChange: false });
          return;
        }
        const item: any = options.find((item: any) => item.label === '元');
        if (!item) return;
        this.paramsForm.get('unit_id')?.setValue(item.value, { emitViewToModelChange: false });
        this.paramsForm.get('unit_name')?.setValue(item.label, { emitViewToModelChange: false });
      }
    });
    this._service.getAddress().subscribe((res) => {
      if (res) {
        this.categoryFormConfig.forEach((item: any) => {
          if (item.type === 'cascader' && item.columnkey === 'receiver_country_name') {
            item.options = this.handleTransOption(res.data.children);
          }
        });
      }
    });
  }
  handleTransOption(value: any) {
    if (value.length) {
      value.forEach((node: any) => {
        node['isLeaf'] = node.is_leaf;
        if (node?.children && node?.children?.length && !node?.is_leaf) {
          this.handleTransOption(node?.children);
        }
      });
      return value;
    }
  }

  handleChangeValue() {}

  onClickBtn(type: any) {
    switch (type) {
      case 'back':
        history.go(-1);
        break;
      case 'cancel':
        this._service.formMode = FormModeEnum.ReadOnly;
        break;
      case 'edit':
        this._service.formMode = FormModeEnum.Edit;
        // 进入编辑模式时，获取币种列表以便编辑
        this.getCurrencyListForEdit();
        break;
      case 'save':
        this._save();
        break;
    }
  }

  _save() {
    if (this._validator.formIsInvalid(this.paramsForm)) {
      // 检查每个字段的验证状态
      Object.keys(this.paramsForm.controls).forEach((key) => {
        const control = this.paramsForm.get(key);
      });
      return;
    }

    const { related_user_ids, related_user_names } = this.paramsForm.value;
    const receiver_country_value = this.paramsForm.get('receiver_country')?.value;
    const params = {
      id: this.id,
      ...this.paramsForm.value,
      status: this.paramsForm.get('status')?.value ? 1 : 2,
      rate: this.paramsForm.get('rate')?.value ? `${this.paramsForm.get('rate')?.value}` : null,
      payment_quota: this.paramsForm.get('payment_quota')?.value ? `${this.paramsForm.get('payment_quota')?.value}` : null,
      receiver_country_id: receiver_country_value?.[0] ?? null,
      province_id: receiver_country_value?.[1] ?? null,
      city_id: receiver_country_value?.[2] ?? null,
      district_id: receiver_country_value?.[3] ?? null,
      related_user: related_user_ids?.map((id: number, i: number) => ({ id, name: related_user_names[i] })),
      payment_quotas: this.assemblePaymentQuotas(),
    };

    this._service.setCustomer(params).subscribe({
      next: (res) => {
        if (res) {
          this.message.create('success', this._service.translateValue('flss.success.save'));
          this._broadcast.yell({ channel: '/basic-archive/custom-record/list', message: {} });
          history.go(-1);
        }
      },
      error: (error) => {
        this.message.create('error', '保存失败，请重试');
      },
    });
  }

  // 组装币种额度数据
  assemblePaymentQuotas() {
    // 组装所有币种数据，包括没有输入额度的数据
    const assembledData = this.paymentQuotas.map((quota) => ({
      id: String(quota.id || 0), // 支付限额表明细ID，转为字符串，新建时为"0"
      currency_id: String(quota.currency_id), // 货币ID，转为字符串
      currency_name: quota.currency_name, // 货币名称
      amount:
        quota.amount !== null && quota.amount !== undefined && quota.amount !== ''
          ? String(quota.amount) // 有值时转为字符串
          : '', // 没有值时设为空字符串
    }));

    return assembledData;
  }

  ngOnDestroy(): void {}

  defaultSysAccountValue: any[] = [];
  onHandleSysAccountSearch(e: any) {
    const user_name: string[] = e?.selectLines?.map((line: any) => line.label);
    this.paramsForm.get('related_user_names')?.setValue(user_name);
  }

  onHandleSearch(e: any, item: any) {
    this.paramsForm.get(item.value)?.setValue(e?.selectLine?.label);
  }

  // 处理币种额度变化
  onQuotaAmountChange(currencyId: number, value: number | null) {
    const quotaIndex = this.paymentQuotas.findIndex((quota) => quota.currency_id === currencyId);
    if (quotaIndex !== -1) {
      this.paymentQuotas[quotaIndex].amount = value;
      // 直接更新表格数据中对应项的值，避免重新创建整个配置对象
      if (this.currencyTableConfig.dataList[quotaIndex]) {
        this.currencyTableConfig.dataList[quotaIndex].amount = value;
      }
    }
  }
}
